# GitWhisper VS Code Extension

🤖 AI-powered Git commit message generator for VS Code

GitWhisper is a VS Code extension that generates meaningful, conventional commit messages using AI. It supports multiple AI providers and languages, making your Git workflow more efficient and consistent.

## ✨ Features

- **🤖 AI-Powered Commit Messages**: Generate conventional commit messages using multiple AI providers
- **🔄 Multiple AI Models**: Support for OpenAI, Claude, Gemini, Grok, Llama, DeepSeek, GitHub Models, and Ollama
- **🚀 Commit and Push**: One-click commit and push to GitHub with Personal Access Token support
- **🌍 Multi-Language Support**: Generate commit messages in 30+ languages
- **⚡ Smart Git Integration**: Automatic staging, multi-repository support, and intelligent change detection
- **🔧 Flexible Configuration**: Customizable settings for models, languages, and workflow preferences
- **🔒 Secure API Key Storage**: Uses VS Code's secure secrets storage for API keys and GitHub PATs
- **📊 Change Analysis**: AI-powered analysis of your code changes
- **🎯 Status Bar Integration**: Quick access and status updates

## 🚀 Quick Start

1. **Install the Extension**

   - Search for "GitWhisper" in VS Code Extensions
   - Or install from the [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=gitwhisper.gitwhisper) (when published)

2. **Set Up Your API Key**

   - Open Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`)
   - Run `GitWhisper: Set API Key`
   - Choose your preferred AI provider and enter your API key

3. **Generate Your First Commit Message**
   - Open Command Palette
   - Run `GitWhisper: Generate Commit Message`
   - Review and commit!

## 🎯 Commands

All commands are accessible through:

- **Command Palette**: Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) and search for "GitWhisper"
- **Source Control Panel**: Look for GitWhisper icons in the SCM toolbar
- **Status Bar**: Click the GitWhisper status item for quick actions

| Command                                        | Description                            |
| ---------------------------------------------- | -------------------------------------- |
| `GitWhisper: Generate Commit Message`          | Generate AI-powered commit message     |
| `GitWhisper: Analyze Changes`                  | Analyze staged changes with AI         |
| `GitWhisper: Set API Key`                      | Configure API keys for AI providers    |
| `GitWhisper: Set GitHub Personal Access Token` | Configure GitHub PAT for private repos |
| `GitWhisper: Select Model`                     | Choose default AI model and variant    |
| `GitWhisper: Set Language`                     | Set commit message language            |
| `GitWhisper: Set Custom Ollama Model`          | Configure custom Ollama model variants |
| `GitWhisper: Open Settings`                    | Open extension settings panel          |

## ⚙️ Configuration

### AI Providers

GitWhisper supports multiple AI providers:

- **OpenAI** (GPT-4o, GPT-4o Mini, GPT-4 Turbo, GPT-4, GPT-3.5 Turbo)
- **Anthropic Claude** (Claude 3.5 Sonnet, Claude 3.5 Haiku, Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku)
- **Google Gemini** (Gemini 1.5 Pro, Gemini 1.5 Flash, Gemini Pro, Gemini Pro Vision)
- **xAI Grok** (Grok Beta, Grok Vision Beta)
- **Meta Llama** (Llama 3.2 90B, Llama 3.2 11B, Llama 3.1 70B, Llama 3.1 8B)
- **DeepSeek** (DeepSeek Chat, DeepSeek Coder)
- **GitHub Models** (GPT-4o, GPT-4o Mini, o1 Preview, o1 Mini)
- **Ollama** (Llama 3.2, Llama 3.1, Code Llama, Mistral, Mixtral, Qwen 2.5, DeepSeek Coder, Phi-3, Gemma 2, and custom models)

### Settings

Configure GitWhisper through VS Code settings:

```json
{
  "gitwhisper.defaultModel": "openai",
  "gitwhisper.defaultVariant": "gpt-4o",
  "gitwhisper.language": "en;US",
  "gitwhisper.alwaysAdd": false,
  "gitwhisper.autoPush": false,
  "gitwhisper.ollamaBaseUrl": "http://localhost:11434",
  "gitwhisper.customOllamaVariant": ""
}
```

### Language Support

Generate commit messages in multiple languages (30+ supported):

**Format**: Use language code format `"code;countryCode"` (e.g., `"en;US"`, `"es;ES"`, `"fr;FR"`)

- **European**: English, Spanish, French, German, Italian, Portuguese, Dutch, Swedish, Norwegian, Danish, Finnish, Polish, Czech, Hungarian, Romanian, Bulgarian, Greek, Turkish, Croatian, Serbian, Slovak, Slovenian, Lithuanian, Latvian, Estonian, Ukrainian, Russian
- **Asian**: Chinese (Simplified), Japanese, Korean, Thai, Vietnamese, Indonesian, Malay
- **Middle Eastern**: Arabic, Hebrew
- **Indian Subcontinent**: Hindi

## 🔧 API Key Setup

### OpenAI

1. Visit [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create a new API key
3. Set it in GitWhisper: `GitWhisper: Set API Key` → `openai`

### Anthropic Claude

1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Generate an API key
3. Set it in GitWhisper: `GitWhisper: Set API Key` → `claude`

### Google Gemini

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create an API key
3. Set it in GitWhisper: `GitWhisper: Set API Key` → `gemini`

### xAI Grok

1. Visit [xAI Console](https://console.x.ai/)
2. Generate an API key
3. Set it in GitWhisper: `GitWhisper: Set API Key` → `grok`

### Meta Llama

1. Visit [Meta Llama](https://llama.meta.com/llama-downloads/)
2. Get API access through approved providers
3. Set it in GitWhisper: `GitWhisper: Set API Key` → `llama`

### DeepSeek

1. Visit [DeepSeek Platform](https://platform.deepseek.com/)
2. Create an API key
3. Set it in GitWhisper: `GitWhisper: Set API Key` → `deepseek`

### GitHub Models

1. Visit [GitHub Models](https://github.com/marketplace/models)
2. Generate a Personal Access Token with model access
3. Set it in GitWhisper: `GitWhisper: Set API Key` → `github`

### Ollama (Local)

1. Install [Ollama](https://ollama.ai/)
2. Pull a model: `ollama pull llama3.2`
3. No API key needed - uses local endpoint
4. Configure custom models in GitWhisper settings if needed

#### Troubleshooting Ollama

If Ollama commit generation is not working:

1. **Check Ollama is running**: Ensure Ollama is installed and running

   ```bash
   ollama serve
   ```

2. **Verify model is available**: Make sure the model is pulled

   ```bash
   ollama list
   ollama pull llama3.2  # or your desired model
   ```

3. **Test Ollama API**: Verify the API is accessible

   ```bash
   curl http://localhost:11434/api/generate -d '{
     "model": "llama3.2",
     "prompt": "Hello",
     "stream": false
   }'
   ```

4. **Check VS Code Developer Console**: Open Developer Tools (Help > Toggle Developer Tools) and check console for detailed error messages

5. **Custom Base URL**: If Ollama runs on different port/host, update base URL in GitWhisper settings

6. **Verbose Output**: If your model (like DeepSeek R1) generates too much text including thinking process, GitWhisper automatically cleans the response to extract just the commit message

   **Setting Custom Ollama Models**: Use `GitWhisper: Set Custom Ollama Model` command to configure specific models like `deepseek-r1:1.5b`, `qwen2.5:7b`, etc.

## 🔗 GitHub Integration

GitWhisper provides seamless GitHub integration with one-click commit and push functionality.

### Setting up GitHub Personal Access Token

For private repositories or enhanced security, you can configure a GitHub Personal Access Token:

1. **Generate a PAT**:

   - Go to [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
   - Click "Generate new token (classic)"
   - Select scopes: `repo` (for private repos) or `public_repo` (for public repos)
   - Copy the generated token

2. **Configure in GitWhisper**:
   - Run `GitWhisper: Set GitHub Personal Access Token`
   - Paste your token (it will be stored securely)

### Commit and Push Workflow

When GitWhisper detects a GitHub repository, you'll see an additional "Commit and Push" button:

1. **Generate commit message** as usual
2. **Choose your action**:
   - **Commit**: Standard commit (local only)
   - **Commit and Push**: Commit and immediately push to GitHub
   - **Copy to Clipboard**: Copy message for manual use

The extension automatically handles:

- GitHub repository detection
- HTTPS/SSH URL conversion
- Secure PAT authentication
- Error handling and fallbacks

## 📝 Usage Examples

### Basic Workflow

```bash
# Make your changes
git add .

# Generate commit message (Command Palette)
GitWhisper: Generate Commit Message

# Review and commit
```

### With Prefix Support

GitWhisper supports commit prefixes for ticket tracking:

```
JIRA-123: feat: add user authentication system
```

### Multi-Repository Support

GitWhisper automatically detects and handles multiple Git repositories in your workspace.

## 🛠️ Development

### Prerequisites

- Node.js 18+
- pnpm
- VS Code 1.102.0 or higher

### Setup

```bash
git clone https://github.com/emacliam/gitwhisper-vscode
cd gitwhisper-vscode
pnpm install
```

### Build

```bash
pnpm run compile
```

### Watch Mode

```bash
pnpm run watch
```

### Package

```bash
pnpm run package
```

### Testing

```bash
pnpm run test
```

### Development Mode

Press `F5` in VS Code to launch a new Extension Development Host window with your extension loaded.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Based on the original [GitWhisper CLI](https://github.com/gitwhisper/gitwhisper) tool
- Inspired by conventional commit standards
- Built with VS Code Extension API
- Uses multiple AI providers for enhanced flexibility

## 📞 Support

- 🐛 [Report Issues](https://github.com/emacliam/gitwhisper-vscode/issues)
- 💬 [Discussions](https://github.com/emacliam/gitwhisper-vscode/discussions)
- 📧 [Email Support](mailto:<EMAIL>)

---

**Happy Committing! 🚀**
