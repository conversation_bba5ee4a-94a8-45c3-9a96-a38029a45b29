.vscode/**
.vscode-test/**
out/**
src/**
.gitignore
.yarnrc
esbuild.js
vsc-extension-quickstart.md
**/tsconfig.json
**/eslint.config.mjs
**/*.map
**/*.ts
**/.vscode-test.*
test-workspace/**
reference/**
*.vsix
.env
.env.*
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
.eslintrc.json
pnpm-lock.yaml
yarn.lock
package-lock.json
**/.DS_Store
**/Thumbs.db
**/*.log
**/*.tmp
**/*.temp
**/coverage/**
**/.nyc_output/**
