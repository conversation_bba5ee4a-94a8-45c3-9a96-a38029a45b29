import * as vscode from "vscode";
import { ConfigManager } from "../config/configManager";
import {
  getSupportedModels,
  getVariantsWithCustom,
  getModelDisplayName,
} from "../models/modelVariants";
import { CommitGeneratorFactory } from "../models/commitGeneratorFactory";
import { getAllLanguages } from "../models/language";

/**
 * Settings webview panel for GitWhisper configuration
 */
export class SettingsPanel {
  public static currentPanel: SettingsPanel | undefined;
  private readonly panel: vscode.WebviewPanel;
  private readonly extensionUri: vscode.Uri;
  private configManager: ConfigManager;
  private disposables: vscode.Disposable[] = [];

  public static createOrShow(
    extensionUri: vscode.Uri,
    configManager: ConfigManager
  ) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // If we already have a panel, show it
    if (SettingsPanel.currentPanel) {
      SettingsPanel.currentPanel.panel.reveal(column);
      return;
    }

    // Otherwise, create a new panel
    const panel = vscode.window.createWebviewPanel(
      "gitwhisperSettings",
      "GitWhisper Settings",
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        localResourceRoots: [extensionUri],
      }
    );

    SettingsPanel.currentPanel = new SettingsPanel(
      panel,
      extensionUri,
      configManager
    );
  }

  private constructor(
    panel: vscode.WebviewPanel,
    extensionUri: vscode.Uri,
    configManager: ConfigManager
  ) {
    this.panel = panel;
    this.extensionUri = extensionUri;
    this.configManager = configManager;

    // Set the webview's initial html content
    this.update();

    // Listen for when the panel is disposed
    this.panel.onDidDispose(() => this.dispose(), null, this.disposables);

    // Handle messages from the webview
    this.panel.webview.onDidReceiveMessage(
      (message) => this.handleMessage(message),
      null,
      this.disposables
    );
  }

  private async handleMessage(message: any) {
    switch (message.command) {
      case "setApiKey":
        await this.configManager.setApiKey(message.model, message.apiKey);
        vscode.window.showInformationMessage(
          `API key for ${message.model} updated`
        );
        this.update(); // Refresh the panel
        break;

      case "removeApiKey":
        await this.configManager.removeApiKey(message.model);
        this.update(); // Refresh the panel
        break;

      case "setDefaults":
        await this.configManager.setDefaults(message.model, message.variant);
        this.update();
        break;

      case "setLanguage":
        const languages = getAllLanguages();
        const language = languages.find((l) => l.code === message.languageCode);
        if (language) {
          await this.configManager.setWhisperLanguage(language);
          this.update();
        }
        break;

      case "setAlwaysAdd":
        await this.configManager.setAlwaysAdd(message.value);
        this.update();
        break;

      case "setAutoPush":
        await this.configManager.setAutoPush(message.value);
        this.update();
        break;

      case "setOllamaUrl":
        await this.configManager.setOllamaBaseURL(message.url);
        this.update();
        break;

      case "setCustomOllamaVariant":
        await this.configManager.setCustomOllamaVariant(message.variant);
        this.update();
        break;
    }
  }

  private async update() {
    const config = await this.configManager.getConfigSummary();
    this.panel.webview.html = this.getHtmlForWebview(config);
  }

  private getHtmlForWebview(config: any): string {
    const models = getSupportedModels();
    const languages = getAllLanguages();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitWhisper Settings</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            background-color: var(--vscode-panel-background);
        }
        .section h2 {
            margin-top: 0;
            color: var(--vscode-textLink-foreground);
            border-bottom: 1px solid var(--vscode-panel-border);
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 3px;
            font-family: inherit;
            font-size: inherit;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status.set {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }
        .status.not-set {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }
        .remove-btn {
            background-color: var(--vscode-errorForeground);
            color: white;
            margin-left: 5px;
        }
        .remove-btn:hover {
            background-color: var(--vscode-errorForeground);
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <h1>GitWhisper Settings</h1>
    
    <div class="section">
        <h2>Default Model Configuration</h2>
        <div class="form-group">
            <label for="defaultModel">Default AI Model:</label>
            <select id="defaultModel" onchange="updateVariants()">
                <option value="">Select a model...</option>
                ${models
                  .map(
                    (model) => `
                    <option value="${model}" ${
                      config.defaultModel === model ? "selected" : ""
                    }>
                        ${getModelDisplayName(model)}
                    </option>
                `
                  )
                  .join("")}
            </select>
        </div>
        <div class="form-group">
            <label for="defaultVariant">Model Variant:</label>
            <select id="defaultVariant">
                <option value="">Select variant...</option>
            </select>
        </div>
        <button onclick="saveDefaults()">Save Default Model</button>
    </div>

    <div class="section">
        <h2>API Keys & Authentication</h2>
        ${models
          .filter(
            (model) =>
              CommitGeneratorFactory.getModelRequirements(model).requiresApiKey
          )
          .map(
            (model) => `
            <div class="form-group">
                <label for="apiKey_${model}">
                    ${getModelDisplayName(model)} ${
              model === "github" ? "Personal Access Token" : "API Key"
            }:
                    <span class="status ${
                      config.hasApiKeys[model] ? "set" : "not-set"
                    }">
                        ${config.hasApiKeys[model] ? "Set" : "Not Set"}
                    </span>
                </label>
                <input type="password" id="apiKey_${model}" placeholder="${
              model === "github"
                ? "Enter GitHub PAT"
                : `Enter API key for ${model}`
            }">
                <button onclick="saveApiKey('${model}')">Save</button>
                ${
                  config.hasApiKeys[model]
                    ? `<button onclick="removeApiKey('${model}')" class="remove-btn">Remove</button>`
                    : ""
                }
                ${
                  model === "github"
                    ? '<br><small>Generate PAT and grant write access (Repository Permissions/Contents, set to read and write) if you want to push using GitWhisper. <a href="https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens" target="_blank">Learn more</a></small>'
                    : ""
                }
            </div>
        `
          )
          .join("")}
    </div>

    <div class="section">
        <h2>Language Settings</h2>
        <div class="form-group">
            <label for="language">Commit Message Language:</label>
            <select id="language" onchange="saveLanguage()">
                ${languages
                  .map(
                    (lang) => `
                    <option value="${lang.code}" ${
                      config.language.code === lang.code ? "selected" : ""
                    }>
                        ${lang.displayName}
                    </option>
                `
                  )
                  .join("")}
            </select>
        </div>
    </div>

    <div class="section">
        <h2>Git Behavior</h2>
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="alwaysAdd" ${
                  config.alwaysAdd ? "checked" : ""
                } onchange="saveAlwaysAdd()">
                <label for="alwaysAdd">Always stage unstaged files before generating commit</label>
            </div>
        </div>
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="autoPush" ${
                  config.autoPush ? "checked" : ""
                } onchange="saveAutoPush()">
                <label for="autoPush">Automatically push commits after committing</label>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Ollama Configuration</h2>
        <div class="form-group">
            <label for="ollamaUrl">Ollama Base URL:</label>
            <input type="text" id="ollamaUrl" value="${
              config.ollamaBaseUrl
            }" placeholder="http://localhost:11434">
            <button onclick="saveOllamaUrl()">Save</button>
        </div>
        <div class="form-group">
            <label for="customOllamaVariant">Custom Ollama Model:</label>
            <input type="text" id="customOllamaVariant" value="${
              config.customOllamaVariant || ""
            }" placeholder="e.g., llama3.2, codellama, mistral (leave empty for predefined variants)">
            <button onclick="saveCustomOllamaVariant()">Save</button>
            <br><small>Enter a custom Ollama model name or leave empty to use predefined variants. Make sure the model is available in your Ollama installation.</small>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        function updateVariants() {
            const modelSelect = document.getElementById('defaultModel');
            const variantSelect = document.getElementById('defaultVariant');
            const selectedModel = modelSelect.value;
            
            // Clear variants
            variantSelect.innerHTML = '<option value="">Select variant...</option>';
            
            if (selectedModel) {
                // Get variants for selected model (this would need to be passed from extension)
                const variants = ${JSON.stringify(
                  models.reduce((acc, model) => {
                    acc[model] = getVariantsWithCustom(
                      model,
                      config.customOllamaVariant
                    );
                    return acc;
                  }, {} as any)
                )};
                
                if (variants[selectedModel]) {
                    variants[selectedModel].forEach(variant => {
                        const option = document.createElement('option');
                        option.value = variant.name;
                        option.textContent = variant.displayName;
                        if ('${config.defaultVariant}' === variant.name) {
                            option.selected = true;
                        }
                        variantSelect.appendChild(option);
                    });
                }
            }
        }
        
        function saveDefaults() {
            const model = document.getElementById('defaultModel').value;
            const variant = document.getElementById('defaultVariant').value;
            
            if (model && variant) {
                vscode.postMessage({
                    command: 'setDefaults',
                    model: model,
                    variant: variant
                });
            }
        }
        
        function saveApiKey(model) {
            const apiKey = document.getElementById('apiKey_' + model).value;
            if (apiKey) {
                vscode.postMessage({
                    command: 'setApiKey',
                    model: model,
                    apiKey: apiKey
                });
                document.getElementById('apiKey_' + model).value = '';
            }
        }
        
        function saveLanguage() {
            const languageCode = document.getElementById('language').value;
            vscode.postMessage({
                command: 'setLanguage',
                languageCode: languageCode
            });
        }
        
        function saveAlwaysAdd() {
            const value = document.getElementById('alwaysAdd').checked;
            vscode.postMessage({
                command: 'setAlwaysAdd',
                value: value
            });
        }
        
        function saveAutoPush() {
            const value = document.getElementById('autoPush').checked;
            vscode.postMessage({
                command: 'setAutoPush',
                value: value
            });
        }
        
        function saveOllamaUrl() {
            const url = document.getElementById('ollamaUrl').value;
            vscode.postMessage({
                command: 'setOllamaUrl',
                url: url
            });
        }

        function removeApiKey(model) {
            if (confirm('Are you sure you want to remove the ' + (model === 'github' ? 'GitHub PAT' : 'API key for ' + model) + '?')) {
                vscode.postMessage({
                    command: 'removeApiKey',
                    model: model
                });
            }
        }

        function saveCustomOllamaVariant() {
            const variant = document.getElementById('customOllamaVariant').value;
            vscode.postMessage({
                command: 'setCustomOllamaVariant',
                variant: variant
            });
        }
        
        // Initialize variants on load
        updateVariants();
    </script>
</body>
</html>`;
  }

  public dispose() {
    SettingsPanel.currentPanel = undefined;

    // Clean up our resources
    this.panel.dispose();

    while (this.disposables.length) {
      const x = this.disposables.pop();
      if (x) {
        x.dispose();
      }
    }
  }
}
