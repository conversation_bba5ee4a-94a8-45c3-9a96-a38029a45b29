import * as vscode from "vscode";
import { ConfigManager } from "../config/configManager";
import {
  getSupportedModels,
  getVariantsWithCustom,
  getModelDisplayName,
} from "../models/modelVariants";
import { CommitGeneratorFactory } from "../models/commitGeneratorFactory";
import { getAllLanguages } from "../models/language";

/**
 * Settings webview panel for GitWhisper configuration
 */
export class SettingsPanel {
  public static currentPanel: SettingsPanel | undefined;
  private readonly panel: vscode.WebviewPanel;
  private readonly extensionUri: vscode.Uri;
  private configManager: ConfigManager;
  private disposables: vscode.Disposable[] = [];

  public static createOrShow(
    extensionUri: vscode.Uri,
    configManager: ConfigManager
  ) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // If we already have a panel, show it
    if (SettingsPanel.currentPanel) {
      SettingsPanel.currentPanel.panel.reveal(column);
      return;
    }

    // Otherwise, create a new panel
    const panel = vscode.window.createWebviewPanel(
      "gitwhisperSettings",
      "GitWhisper Settings",
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        localResourceRoots: [extensionUri],
      }
    );

    SettingsPanel.currentPanel = new SettingsPanel(
      panel,
      extensionUri,
      configManager
    );
  }

  private constructor(
    panel: vscode.WebviewPanel,
    extensionUri: vscode.Uri,
    configManager: ConfigManager
  ) {
    this.panel = panel;
    this.extensionUri = extensionUri;
    this.configManager = configManager;

    // Set the webview's initial html content
    this.update();

    // Listen for when the panel is disposed
    this.panel.onDidDispose(() => this.dispose(), null, this.disposables);

    // Handle messages from the webview
    this.panel.webview.onDidReceiveMessage(
      (message) => this.handleMessage(message),
      null,
      this.disposables
    );
  }

  private async handleMessage(message: any) {
    switch (message.command) {
      case "setApiKey":
        await this.configManager.setApiKey(message.model, message.apiKey);
        vscode.window.showInformationMessage(
          `API key for ${message.model} updated`
        );
        this.update(); // Refresh the panel
        break;

      case "removeApiKey":
        await this.configManager.removeApiKey(message.model);
        this.update(); // Refresh the panel
        break;

      case "setDefaults":
        await this.configManager.setDefaults(message.model, message.variant);
        this.update();
        break;

      case "setLanguage":
        const languages = getAllLanguages();
        const language = languages.find((l) => l.code === message.languageCode);
        if (language) {
          await this.configManager.setWhisperLanguage(language);
          this.update();
        }
        break;

      case "setAlwaysAdd":
        await this.configManager.setAlwaysAdd(message.value);
        this.update();
        break;

      case "setAutoPush":
        await this.configManager.setAutoPush(message.value);
        this.update();
        break;

      case "setOllamaUrl":
        await this.configManager.setOllamaBaseURL(message.url);
        this.update();
        break;

      case "setCustomOllamaVariant":
        await this.configManager.setCustomOllamaVariant(message.variant);
        this.update();
        break;

      case "setKeyboardShortcuts":
        await this.configManager.setKeyboardShortcuts(message.shortcuts);
        this.update();
        break;

      case "setIgnoredFiles":
        await this.configManager.setIgnoredFiles(message.patterns);
        this.update();
        break;

      case "addIgnoredFile":
        await this.configManager.addIgnoredFile(message.pattern);
        this.update();
        break;

      case "removeIgnoredFile":
        await this.configManager.removeIgnoredFile(message.pattern);
        this.update();
        break;
    }
  }

  private async update() {
    const config = await this.configManager.getConfigSummary();
    this.panel.webview.html = this.getHtmlForWebview(config);
  }

  private getHtmlForWebview(config: any): string {
    const models = getSupportedModels();
    const languages = getAllLanguages();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitWhisper Settings</title>
    <style>
        :root {
            --gw-primary: #007acc;
            --gw-primary-hover: #005a9e;
            --gw-success: #28a745;
            --gw-warning: #ffc107;
            --gw-danger: #dc3545;
            --gw-info: #17a2b8;
            --gw-gradient: linear-gradient(135deg, var(--gw-primary), #4a9eff);
            --gw-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            --gw-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
            --gw-border-radius: 8px;
            --gw-transition: all 0.2s ease-in-out;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: var(--vscode-font-family), 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background: linear-gradient(135deg, var(--vscode-editor-background) 0%, var(--vscode-sideBar-background) 100%);
            padding: 24px;
            line-height: 1.6;
            margin: 0;
            min-height: 100vh;
        }

        h1 {
            text-align: center;
            margin-bottom: 32px;
            font-size: 2.2em;
            font-weight: 300;
            background: var(--gw-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--gw-gradient);
            border-radius: 2px;
        }

        .section {
            margin-bottom: 32px;
            padding: 24px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: var(--gw-border-radius);
            background: var(--vscode-panel-background);
            box-shadow: var(--gw-shadow);
            transition: var(--gw-transition);
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gw-gradient);
        }

        .section:hover {
            box-shadow: var(--gw-shadow-hover);
            transform: translateY(-2px);
        }

        .section h2 {
            margin-top: 0;
            margin-bottom: 20px;
            color: var(--vscode-textLink-foreground);
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h2::before {
            content: '⚙️';
            font-size: 0.9em;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--vscode-foreground);
            font-size: 0.95em;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: var(--gw-border-radius);
            font-family: inherit;
            font-size: inherit;
            transition: var(--gw-transition);
            outline: none;
        }

        input:focus, select:focus, textarea:focus {
            border-color: var(--gw-primary);
            box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
        }

        input:hover, select:hover, textarea:hover {
            border-color: var(--gw-primary);
        }

        button {
            background: var(--gw-gradient);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--gw-border-radius);
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            font-weight: 600;
            transition: var(--gw-transition);
            box-shadow: var(--gw-shadow);
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: var(--gw-shadow-hover);
        }

        button:active {
            transform: translateY(0);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: var(--gw-border-radius);
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-input-border);
            transition: var(--gw-transition);
        }

        .checkbox-group:hover {
            border-color: var(--gw-primary);
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--gw-primary);
        }

        .status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status.set {
            background-color: var(--gw-success);
            color: white;
        }

        .status.set::before {
            content: '✓';
        }

        .status.not-set {
            background-color: var(--gw-warning);
            color: white;
        }

        .status.not-set::before {
            content: '⚠';
        }

        .remove-btn {
            background: linear-gradient(135deg, var(--gw-danger), #ff6b6b);
            color: white;
            margin-left: 8px;
            padding: 8px 16px;
        }

        .remove-btn:hover {
            background: linear-gradient(135deg, #c82333, var(--gw-danger));
            transform: translateY(-1px);
        }

        .button-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 16px;
        }

        .input-group {
            display: flex;
            gap: 8px;
            align-items: end;
        }

        .input-group input {
            flex: 1;
        }

        .input-group button {
            flex-shrink: 0;
        }

        small {
            display: block;
            margin-top: 8px;
            color: var(--vscode-descriptionForeground);
            font-size: 0.85em;
            line-height: 1.4;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading {
            position: relative;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--gw-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>GitWhisper Settings</h1>
    
    <div class="section fade-in">
        <h2>🤖 Default Model Configuration</h2>
        <div class="form-row">
            <div class="form-group">
                <label for="defaultModel">Default AI Model:</label>
                <select id="defaultModel" onchange="updateVariants()">
                    <option value="">Select a model...</option>
                    ${models
                      .map(
                        (model) => `
                        <option value="${model}" ${
                          config.defaultModel === model ? "selected" : ""
                        }>
                            ${getModelDisplayName(model)}
                        </option>
                    `
                      )
                      .join("")}
                </select>
            </div>
            <div class="form-group">
                <label for="defaultVariant">Model Variant:</label>
                <select id="defaultVariant">
                    <option value="">Select variant...</option>
                </select>
            </div>
        </div>
        <div class="button-group">
            <button onclick="saveDefaults()">💾 Save Default Model</button>
        </div>
    </div>

    <div class="section fade-in">
        <h2>🔐 API Keys & Authentication</h2>
        ${models
          .filter(
            (model) =>
              CommitGeneratorFactory.getModelRequirements(model).requiresApiKey
          )
          .map(
            (model) => `
            <div class="form-group">
                <label for="apiKey_${model}">
                    ${getModelDisplayName(model)} ${
              model === "github" ? "Personal Access Token" : "API Key"
            }:
                    <span class="status ${
                      config.hasApiKeys[model] ? "set" : "not-set"
                    }">
                        ${config.hasApiKeys[model] ? "Set" : "Not Set"}
                    </span>
                </label>
                <div class="input-group">
                    <input type="password" id="apiKey_${model}" placeholder="${
              model === "github"
                ? "Enter GitHub PAT"
                : `Enter API key for ${model}`
            }">
                    <button onclick="saveApiKey('${model}')">💾 Save</button>
                    ${
                      config.hasApiKeys[model]
                        ? `<button onclick="removeApiKey('${model}')" class="remove-btn">🗑️ Remove</button>`
                        : ""
                    }
                </div>
                ${
                  model === "github"
                    ? '<small>Generate PAT and grant write access (Repository Permissions/Contents, set to read and write) if you want to push using GitWhisper. <a href="https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens" target="_blank">Learn more</a></small>'
                    : ""
                }
            </div>
        `
          )
          .join("")}
    </div>

    <div class="section fade-in">
        <h2>🌐 Language Settings</h2>
        <div class="form-group">
            <label for="language">Commit Message Language:</label>
            <select id="language" onchange="saveLanguage()">
                ${languages
                  .map(
                    (lang) => `
                    <option value="${lang.code}" ${
                      config.language.code === lang.code ? "selected" : ""
                    }>
                        ${lang.displayName}
                    </option>
                `
                  )
                  .join("")}
            </select>
        </div>
    </div>

    <div class="section fade-in">
        <h2>⚙️ Git Behavior</h2>
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="alwaysAdd" ${
                  config.alwaysAdd ? "checked" : ""
                } onchange="saveAlwaysAdd()">
                <label for="alwaysAdd">Always stage unstaged files before generating commit</label>
            </div>
        </div>
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="autoPush" ${
                  config.autoPush ? "checked" : ""
                } onchange="saveAutoPush()">
                <label for="autoPush">Automatically push commits after committing</label>
            </div>
        </div>
    </div>

    <div class="section fade-in">
        <h2>🦙 Ollama Configuration</h2>
        <div class="form-group">
            <label for="ollamaUrl">Ollama Base URL:</label>
            <div class="input-group">
                <input type="text" id="ollamaUrl" value="${
                  config.ollamaBaseUrl
                }" placeholder="http://localhost:11434">
                <button onclick="saveOllamaUrl()">💾 Save</button>
            </div>
        </div>
        <div class="form-group">
            <label for="customOllamaVariant">Custom Ollama Model:</label>
            <div class="input-group">
                <input type="text" id="customOllamaVariant" value="${
                  config.customOllamaVariant || ""
                }" placeholder="e.g., llama3.2, codellama, mistral (leave empty for predefined variants)">
                <button onclick="saveCustomOllamaVariant()">💾 Save</button>
            </div>
            <small>Enter a custom Ollama model name or leave empty to use predefined variants. Make sure the model is available in your Ollama installation.</small>
        </div>
    </div>

    <div class="section fade-in">
        <h2>⌨️ Keyboard Shortcuts</h2>
        <div class="form-group">
            <label>Custom Keyboard Shortcuts (format: G+key):</label>
            <small>Configure custom keyboard shortcuts starting with 'G' prefix. Examples: G+C, G+A, G+F</small>
        </div>
        ${Object.entries(config.keyboardShortcuts)
          .map(
            ([command, shortcut]) => `
            <div class="form-group">
                <label for="shortcut_${command}">${command
              .replace(/([A-Z])/g, " $1")
              .replace(/^./, (str) => str.toUpperCase())}:</label>
                <div class="input-group">
                    <input type="text" id="shortcut_${command}" value="${shortcut}"
                           placeholder="G+key" pattern="G\\+[A-Z]" maxlength="3">
                    <button onclick="updateShortcut('${command}')">💾 Save</button>
                </div>
            </div>
        `
          )
          .join("")}
        <div class="button-group">
            <button onclick="resetShortcuts()">🔄 Reset to Defaults</button>
            <button onclick="saveAllShortcuts()">💾 Save All Shortcuts</button>
        </div>
    </div>

    <div class="section fade-in">
        <h2>🚫 Ignored Files</h2>
        <div class="form-group">
            <label>File Patterns to Ignore:</label>
            <small>Files matching these patterns will be excluded from commit message generation. Supports glob patterns like *.log, *.tmp</small>
        </div>
        <div class="form-group">
            <label for="newIgnorePattern">Add New Pattern:</label>
            <div class="input-group">
                <input type="text" id="newIgnorePattern" placeholder="e.g., *.log, node_modules/*, .env">
                <button onclick="addIgnorePattern()">➕ Add</button>
            </div>
        </div>
        <div class="form-group">
            <label>Current Ignored Patterns:</label>
            <div id="ignoredFilesList" style="margin-top: 12px;">
                ${config.ignoredFiles
                  .map(
                    (pattern: string) => `
                    <div class="checkbox-group" style="justify-content: space-between;">
                        <span style="font-family: monospace; background: var(--vscode-editor-background); padding: 4px 8px; border-radius: 4px;">${pattern}</span>
                        <button onclick="removeIgnorePattern('${pattern}')" class="remove-btn" style="margin-left: 0; padding: 4px 8px; font-size: 0.8em;">🗑️ Remove</button>
                    </div>
                `
                  )
                  .join("")}
            </div>
        </div>
        <div class="button-group">
            <button onclick="resetIgnoredFiles()">🔄 Reset to Defaults</button>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        function updateVariants() {
            const modelSelect = document.getElementById('defaultModel');
            const variantSelect = document.getElementById('defaultVariant');
            const selectedModel = modelSelect.value;
            
            // Clear variants
            variantSelect.innerHTML = '<option value="">Select variant...</option>';
            
            if (selectedModel) {
                // Get variants for selected model (this would need to be passed from extension)
                const variants = ${JSON.stringify(
                  models.reduce((acc, model) => {
                    acc[model] = getVariantsWithCustom(
                      model,
                      config.customOllamaVariant
                    );
                    return acc;
                  }, {} as any)
                )};
                
                if (variants[selectedModel]) {
                    variants[selectedModel].forEach(variant => {
                        const option = document.createElement('option');
                        option.value = variant.name;
                        option.textContent = variant.displayName;
                        if ('${config.defaultVariant}' === variant.name) {
                            option.selected = true;
                        }
                        variantSelect.appendChild(option);
                    });
                }
            }
        }
        
        function saveDefaults() {
            const model = document.getElementById('defaultModel').value;
            const variant = document.getElementById('defaultVariant').value;
            
            if (model && variant) {
                vscode.postMessage({
                    command: 'setDefaults',
                    model: model,
                    variant: variant
                });
            }
        }
        
        function saveApiKey(model) {
            const apiKey = document.getElementById('apiKey_' + model).value;
            if (apiKey) {
                vscode.postMessage({
                    command: 'setApiKey',
                    model: model,
                    apiKey: apiKey
                });
                document.getElementById('apiKey_' + model).value = '';
            }
        }
        
        function saveLanguage() {
            const languageCode = document.getElementById('language').value;
            vscode.postMessage({
                command: 'setLanguage',
                languageCode: languageCode
            });
        }
        
        function saveAlwaysAdd() {
            const value = document.getElementById('alwaysAdd').checked;
            vscode.postMessage({
                command: 'setAlwaysAdd',
                value: value
            });
        }
        
        function saveAutoPush() {
            const value = document.getElementById('autoPush').checked;
            vscode.postMessage({
                command: 'setAutoPush',
                value: value
            });
        }
        
        function saveOllamaUrl() {
            const url = document.getElementById('ollamaUrl').value;
            vscode.postMessage({
                command: 'setOllamaUrl',
                url: url
            });
        }

        function removeApiKey(model) {
            if (confirm('Are you sure you want to remove the ' + (model === 'github' ? 'GitHub PAT' : 'API key for ' + model) + '?')) {
                vscode.postMessage({
                    command: 'removeApiKey',
                    model: model
                });
            }
        }

        function saveCustomOllamaVariant() {
            const variant = document.getElementById('customOllamaVariant').value;
            vscode.postMessage({
                command: 'setCustomOllamaVariant',
                variant: variant
            });
        }

        // Keyboard shortcuts functions
        function updateShortcut(command) {
            const shortcut = document.getElementById('shortcut_' + command).value;
            if (shortcut && /^G\+[A-Z]$/.test(shortcut)) {
                const shortcuts = ${JSON.stringify(config.keyboardShortcuts)};
                shortcuts[command] = shortcut;
                vscode.postMessage({
                    command: 'setKeyboardShortcuts',
                    shortcuts: shortcuts
                });
            } else {
                alert('Invalid shortcut format. Use G+key format (e.g., G+C, G+A)');
            }
        }

        function saveAllShortcuts() {
            const shortcuts = {};
            const commands = ${JSON.stringify(
              Object.keys(config.keyboardShortcuts)
            )};
            let isValid = true;

            commands.forEach(command => {
                const shortcut = document.getElementById('shortcut_' + command).value;
                if (shortcut && /^G\+[A-Z]$/.test(shortcut)) {
                    shortcuts[command] = shortcut;
                } else {
                    isValid = false;
                }
            });

            if (isValid) {
                vscode.postMessage({
                    command: 'setKeyboardShortcuts',
                    shortcuts: shortcuts
                });
            } else {
                alert('Some shortcuts have invalid format. Use G+key format (e.g., G+C, G+A)');
            }
        }

        function resetShortcuts() {
            if (confirm('Reset all keyboard shortcuts to defaults?')) {
                const defaultShortcuts = {
                    generateCommit: "G+C",
                    analyzeChanges: "G+A",
                    quickCommitFix: "G+F",
                    quickCommitFeature: "G+N",
                    quickCommitRefactor: "G+R",
                    quickCommitDocs: "G+D",
                    quickCommitTest: "G+T",
                    quickCommitChore: "G+H"
                };
                vscode.postMessage({
                    command: 'setKeyboardShortcuts',
                    shortcuts: defaultShortcuts
                });
            }
        }

        // Ignored files functions
        function addIgnorePattern() {
            const pattern = document.getElementById('newIgnorePattern').value.trim();
            if (pattern) {
                vscode.postMessage({
                    command: 'addIgnoredFile',
                    pattern: pattern
                });
                document.getElementById('newIgnorePattern').value = '';
            }
        }

        function removeIgnorePattern(pattern) {
            if (confirm('Remove ignore pattern: ' + pattern + '?')) {
                vscode.postMessage({
                    command: 'removeIgnoredFile',
                    pattern: pattern
                });
            }
        }

        function resetIgnoredFiles() {
            if (confirm('Reset ignored files to defaults?')) {
                const defaultPatterns = [
                    "package-lock.json",
                    "yarn.lock",
                    "pnpm-lock.yaml",
                    "*.log",
                    "*.tmp",
                    "*.temp",
                    ".DS_Store",
                    "Thumbs.db"
                ];
                vscode.postMessage({
                    command: 'setIgnoredFiles',
                    patterns: defaultPatterns
                });
            }
        }

        // Initialize variants on load
        updateVariants();
    </script>
</body>
</html>`;
  }

  public dispose() {
    SettingsPanel.currentPanel = undefined;

    // Clean up our resources
    this.panel.dispose();

    while (this.disposables.length) {
      const x = this.disposables.pop();
      if (x) {
        x.dispose();
      }
    }
  }
}
