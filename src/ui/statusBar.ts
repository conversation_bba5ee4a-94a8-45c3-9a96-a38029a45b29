import * as vscode from 'vscode';
import { ConfigManager } from '../config/configManager';
import { CommitGeneratorFactory } from '../models/commitGeneratorFactory';

/**
 * Status bar manager for GitWhisper extension
 * Shows current model, language, and provides quick access to commands
 */
export class StatusBarManager {
    private statusBarItem: vscode.StatusBarItem;
    private configManager: ConfigManager;

    constructor(configManager: ConfigManager) {
        this.configManager = configManager;
        this.statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Left,
            100
        );
        this.statusBarItem.command = 'gitwhisper.statusBarClick';
        this.updateStatusBar();
    }

    /**
     * Update the status bar with current configuration
     */
    async updateStatusBar(): Promise<void> {
        try {
            const config = await this.configManager.getConfigSummary();
            const modelName = config.defaultModel || 'No Model';
            const language = config.language.code.toUpperCase();
            
            // Show model and language
            this.statusBarItem.text = `$(robot) ${modelName} | $(globe) ${language}`;
            this.statusBarItem.tooltip = this.createTooltip(config);
            this.statusBarItem.show();
        } catch (error) {
            console.error('Error updating status bar:', error);
            this.statusBarItem.text = '$(robot) GitWhisper';
            this.statusBarItem.tooltip = 'GitWhisper - Click to configure';
            this.statusBarItem.show();
        }
    }

    /**
     * Create detailed tooltip for status bar
     */
    private createTooltip(config: any): string {
        const lines = [
            'GitWhisper - AI-Powered Git Commits',
            '',
            `Model: ${config.defaultModel || 'Not set'}`,
            `Variant: ${config.defaultVariant || 'Not set'}`,
            `Language: ${config.language.displayName}`,
            `Auto Add: ${config.alwaysAdd ? 'On' : 'Off'}`,
            `Auto Push: ${config.autoPush ? 'On' : 'Off'}`,
            '',
            'Click for quick actions'
        ];
        return lines.join('\n');
    }

    /**
     * Handle status bar click - show quick actions menu
     */
    async handleStatusBarClick(): Promise<void> {
        const config = await this.configManager.getConfigSummary();
        
        const quickActions = [
            {
                label: '$(git-commit) Generate Commit Message',
                description: 'Generate AI-powered commit message for staged changes',
                command: 'gitwhisper.generateCommit'
            },
            {
                label: '$(search) Analyze Changes',
                description: 'Get AI analysis of your staged changes',
                command: 'gitwhisper.analyzeChanges'
            },
            {
                label: '$(gear) Configure GitWhisper',
                description: 'View and modify extension settings',
                command: 'gitwhisper.configure'
            },
            {
                label: '$(key) Set API Key',
                description: 'Set or update API keys for AI models',
                command: 'gitwhisper.setApiKey'
            },
            {
                label: '$(robot) Select Model',
                description: `Current: ${config.defaultModel || 'None'}`,
                command: 'gitwhisper.selectModel'
            },
            {
                label: '$(globe) Set Language',
                description: `Current: ${config.language.displayName}`,
                command: 'gitwhisper.setLanguage'
            }
        ];

        const selectedAction = await vscode.window.showQuickPick(quickActions, {
            placeHolder: 'GitWhisper - Select an action',
            matchOnDescription: true
        });

        if (selectedAction) {
            await vscode.commands.executeCommand(selectedAction.command);
            // Update status bar after command execution
            setTimeout(() => this.updateStatusBar(), 500);
        }
    }

    /**
     * Register the status bar click command
     */
    registerCommand(context: vscode.ExtensionContext): void {
        const command = vscode.commands.registerCommand(
            'gitwhisper.statusBarClick',
            () => this.handleStatusBarClick()
        );
        context.subscriptions.push(command);
    }

    /**
     * Show temporary status message
     */
    showTemporaryStatus(message: string, duration: number = 3000): void {
        const originalText = this.statusBarItem.text;
        const originalTooltip = this.statusBarItem.tooltip;
        
        this.statusBarItem.text = `$(info) ${message}`;
        this.statusBarItem.tooltip = message;
        
        setTimeout(() => {
            this.statusBarItem.text = originalText;
            this.statusBarItem.tooltip = originalTooltip;
        }, duration);
    }

    /**
     * Show error status
     */
    showError(message: string, duration: number = 5000): void {
        const originalText = this.statusBarItem.text;
        const originalTooltip = this.statusBarItem.tooltip;
        
        this.statusBarItem.text = `$(error) Error`;
        this.statusBarItem.tooltip = `GitWhisper Error: ${message}`;
        
        setTimeout(() => {
            this.statusBarItem.text = originalText;
            this.statusBarItem.tooltip = originalTooltip;
        }, duration);
    }

    /**
     * Show success status
     */
    showSuccess(message: string, duration: number = 3000): void {
        const originalText = this.statusBarItem.text;
        const originalTooltip = this.statusBarItem.tooltip;
        
        this.statusBarItem.text = `$(check) ${message}`;
        this.statusBarItem.tooltip = `GitWhisper: ${message}`;
        
        setTimeout(() => {
            this.statusBarItem.text = originalText;
            this.statusBarItem.tooltip = originalTooltip;
        }, duration);
    }

    /**
     * Hide the status bar
     */
    hide(): void {
        this.statusBarItem.hide();
    }

    /**
     * Show the status bar
     */
    show(): void {
        this.statusBarItem.show();
    }

    /**
     * Dispose of the status bar
     */
    dispose(): void {
        this.statusBarItem.dispose();
    }
}
