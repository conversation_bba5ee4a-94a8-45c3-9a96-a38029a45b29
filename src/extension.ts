import * as vscode from "vscode";
import { GitUtils } from "./utils/gitUtils";
import { ConfigManager } from "./config/configManager";
import { CommitGeneratorFactory } from "./models/commitGeneratorFactory";
import { getAllLanguages } from "./models/language";
import {
  getVariantsWithCustom,
  getDefaultVariant,
} from "./models/modelVariants";
import { StatusBarManager } from "./ui/statusBar";
import { SettingsPanel } from "./ui/settingsPanel";
import { ErrorHandler, ApiException } from "./utils/errorHandler";
import { ValidationUtils } from "./utils/validation";
import { DiffChunker } from "./utils/diffChunker";

export function activate(context: vscode.ExtensionContext) {
  console.log("GitWhisper extension is now active!");

  // Initialize the config manager with extension context
  ConfigManager.initialize(context);
  const configManager = new ConfigManager();

  // Initialize status bar
  const statusBarManager = new StatusBarManager(configManager);
  statusBarManager.registerCommand(context);
  context.subscriptions.push(statusBarManager);

  // Register commands
  const commands = [
    vscode.commands.registerCommand("gitwhisper.generateCommit", () =>
      generateCommitMessage(configManager, statusBarManager)
    ),
    vscode.commands.registerCommand("gitwhisper.analyzeChanges", () =>
      analyzeChanges(configManager, statusBarManager)
    ),
    vscode.commands.registerCommand("gitwhisper.configure", () =>
      showConfiguration(configManager, context)
    ),
    vscode.commands.registerCommand("gitwhisper.setApiKey", () =>
      setApiKey(configManager, statusBarManager)
    ),
    vscode.commands.registerCommand("gitwhisper.selectModel", () =>
      selectModel(configManager, statusBarManager)
    ),
    vscode.commands.registerCommand("gitwhisper.setLanguage", () =>
      setLanguage(configManager, statusBarManager)
    ),
    vscode.commands.registerCommand("gitwhisper.openSettings", () =>
      SettingsPanel.createOrShow(context.extensionUri, configManager)
    ),
    vscode.commands.registerCommand("gitwhisper.setGitHubPAT", () =>
      setGitHubPAT(configManager, statusBarManager)
    ),
    vscode.commands.registerCommand("gitwhisper.setCustomOllamaVariant", () =>
      setCustomOllamaVariant(configManager, statusBarManager)
    ),
  ];

  context.subscriptions.push(...commands);
}

/**
 * Generate AI-powered commit message
 */
async function generateCommitMessage(
  configManager: ConfigManager,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    const workspaceFolder = getWorkspaceFolder();
    if (!workspaceFolder) {
      return;
    }

    const gitUtils = new GitUtils(workspaceFolder.uri.fsPath);

    // Check if we're in a git repository
    if (!(await gitUtils.isGitRepository())) {
      const subRepos = await gitUtils.findGitReposInSubfolders();
      if (subRepos.length === 0) {
        vscode.window.showErrorMessage(
          "No git repository found. Please run in a git repository or initialize one with `git init`."
        );
        return;
      }

      const choice = await vscode.window.showQuickPick(
        ["Continue with subfolders", "Cancel"],
        { placeHolder: "Git repositories found in subfolders. Continue?" }
      );

      if (choice !== "Continue with subfolders") {
        return;
      }
    }

    // Check for staged changes
    const hasStagedChanges = await gitUtils.hasStagedChanges();
    if (!hasStagedChanges) {
      if (configManager.shouldAlwaysAdd()) {
        const hasUnstagedChanges = await gitUtils.hasUnstagedChanges();
        if (hasUnstagedChanges) {
          vscode.window.showInformationMessage("Staging unstaged changes...");
          const stagedCount = await gitUtils.stageAllUnstagedFilesAndCount();
          vscode.window.showInformationMessage(`${stagedCount} files staged.`);
        } else {
          vscode.window.showErrorMessage(
            "No staged or unstaged changes found!"
          );
          return;
        }
      } else {
        // Check if there are unstaged changes
        const hasUnstagedChanges = await gitUtils.hasUnstagedChanges();
        if (hasUnstagedChanges) {
          const action = await vscode.window.showWarningMessage(
            "No staged changes found. Would you like to stage all changes automatically?",
            "Stage All & Continue",
            "Stage Manually",
            "Cancel"
          );

          if (action === "Stage All & Continue") {
            vscode.window.showInformationMessage("Staging all changes...");
            const stagedCount = await gitUtils.stageAllUnstagedFilesAndCount();
            vscode.window.showInformationMessage(
              `${stagedCount} files staged.`
            );
          } else if (action === "Stage Manually") {
            vscode.window.showInformationMessage(
              "Please stage your changes using `git add` and try again."
            );
            return;
          } else {
            return;
          }
        } else {
          vscode.window.showErrorMessage(
            "No staged or unstaged changes found!"
          );
          return;
        }
      }
    }

    // Get model configuration
    const { modelName, variant, apiKey } = await getModelConfiguration(
      configManager
    );
    if (!modelName) {
      return;
    }

    // Get prefix if needed
    const prefix = await vscode.window.showInputBox({
      prompt: "Enter commit prefix (optional, e.g., JIRA-123)",
      placeHolder: "Leave empty for no prefix",
    });

    // Get staged diff with file filtering
    const ignorePatterns = configManager.getIgnoredFiles();
    const diffResult = await gitUtils.getFilteredStagedDiff(ignorePatterns);
    const diff = diffResult.diff;

    if (!diff) {
      vscode.window.showErrorMessage("No changes detected in staged files.");
      return;
    }

    // Show filtering summary if files were ignored
    if (diffResult.ignoredFiles.length > 0) {
      const message = `Ignored ${
        diffResult.ignoredFiles.length
      } file(s): ${diffResult.ignoredFiles.slice(0, 3).join(", ")}${
        diffResult.ignoredFiles.length > 3
          ? ` and ${diffResult.ignoredFiles.length - 3} more`
          : ""
      }`;
      vscode.window.showInformationMessage(message);
    }

    // Check if diff needs chunking
    const needsChunking = DiffChunker.needsChunking(diff);

    if (needsChunking) {
      // Handle large diff with chunking
      await handleChunkedCommitGeneration(
        diff,
        modelName,
        apiKey,
        variant,
        configManager,
        gitUtils,
        statusBarManager,
        prefix
      );
    } else {
      // Handle normal diff
      await handleSingleCommitGeneration(
        diff,
        modelName,
        apiKey,
        variant,
        configManager,
        gitUtils,
        statusBarManager,
        prefix
      );
    }
  } catch (error: any) {
    vscode.window.showErrorMessage(`Error: ${error.message}`);
  }
}

/**
 * Handle single commit generation for normal-sized diffs
 */
async function handleSingleCommitGeneration(
  diff: string,
  modelName: string,
  apiKey: string | undefined,
  variant: string | undefined,
  configManager: ConfigManager,
  gitUtils: GitUtils,
  statusBarManager?: StatusBarManager,
  prefix?: string
): Promise<void> {
  // Generate commit message
  vscode.window.withProgress(
    {
      location: vscode.ProgressLocation.Notification,
      title: `Generating commit message using ${modelName}...`,
      cancellable: false,
    },
    async () => {
      try {
        const generator = CommitGeneratorFactory.create(modelName, apiKey, {
          variant,
          baseUrl:
            modelName === "ollama"
              ? configManager.getOllamaBaseURL()
              : undefined,
        });
        const language = configManager.getWhisperLanguage();

        const commitMessage = await generator.generateCommitMessage(
          diff,
          language,
          prefix
        );
        let cleanedMessage = GitUtils.stripMarkdownCodeBlocks(commitMessage);

        // Auto-fix commit message length if too long
        cleanedMessage = autoFixCommitMessage(cleanedMessage);

        // Validate the generated commit message
        const messageValidation =
          ValidationUtils.validateCommitMessage(cleanedMessage);
        if (!messageValidation.isValid) {
          ValidationUtils.showValidationError(
            messageValidation,
            "in generated commit message"
          );
          return;
        }

        // Show warning if any
        if (messageValidation.suggestion) {
          ValidationUtils.showValidationWarning(
            messageValidation,
            "Generated commit message"
          );
        }

        // Check if this is a GitHub repository
        const isGitHub = await gitUtils.isGitHubRepository();

        // Show the generated message and ask for confirmation
        let finalMessage = cleanedMessage;
        let userAction: string | undefined;

        do {
          const buttons = [
            "Commit",
            "Edit Message",
            "Copy to Clipboard",
            "Cancel",
          ];
          if (isGitHub) {
            buttons.splice(1, 0, "Commit and Push"); // Insert after "Commit"
          }

          userAction = await vscode.window.showInformationMessage(
            `Generated commit message:\n\n${finalMessage}`,
            { modal: true },
            ...buttons
          );

          if (userAction === "Edit Message") {
            const editedMessage = await vscode.window.showInputBox({
              prompt: "Edit the commit message",
              value: finalMessage,
              placeHolder: "Enter your commit message",
              ignoreFocusOut: true,
              validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                  return "Commit message cannot be empty";
                }
                const validation = ValidationUtils.validateCommitMessage(
                  value.trim()
                );
                if (!validation.isValid) {
                  return validation.error;
                }
                return null;
              },
            });

            if (editedMessage !== undefined) {
              finalMessage = editedMessage.trim();
              // Validate the edited message
              const messageValidation =
                ValidationUtils.validateCommitMessage(finalMessage);
              if (messageValidation.suggestion) {
                ValidationUtils.showValidationWarning(
                  messageValidation,
                  "Edited commit message"
                );
              }
            }
            // Continue the loop to show the dialog again with the edited message
          }
        } while (userAction === "Edit Message");

        const action = userAction;

        if (action === "Commit") {
          const autoPush = configManager.shouldAutoPush();
          const githubPAT = autoPush
            ? await configManager.getGitHubPAT()
            : undefined;
          await gitUtils.runGitCommit({
            message: finalMessage,
            autoPush,
            githubPAT,
          });
          statusBarManager?.showSuccess("Commit successful!");
        } else if (action === "Commit and Push") {
          await handleCommitAndPush(
            configManager,
            gitUtils,
            finalMessage,
            statusBarManager
          );
        } else if (action === "Copy to Clipboard") {
          await vscode.env.clipboard.writeText(finalMessage);
          vscode.window.showInformationMessage(
            "Commit message copied to clipboard!"
          );
          statusBarManager?.showSuccess("Copied to clipboard!");
        }
      } catch (error: any) {
        if (error instanceof ApiException) {
          ErrorHandler.handleErrorWithRetry(error, "generating commit message");

          if (ErrorHandler.shouldSuggestModelSwitch(error)) {
            const suggestions = ErrorHandler.getModelSwitchSuggestions(error);
            ErrorHandler.handleErrorWithFallback(
              error,
              "generating commit message",
              suggestions
            );
          }
        } else {
          ErrorHandler.handleGeneralError(error, "generating commit message");
        }
        statusBarManager?.showError(error.message);
      }
    }
  );
}

/**
 * Handle chunked commit generation for large diffs
 */
async function handleChunkedCommitGeneration(
  diff: string,
  modelName: string,
  apiKey: string | undefined,
  variant: string | undefined,
  configManager: ConfigManager,
  gitUtils: GitUtils,
  statusBarManager?: StatusBarManager,
  prefix?: string
): Promise<void> {
  try {
    // Split diff into chunks
    const chunks = DiffChunker.chunkDiff(diff);
    const chunkingSummary = DiffChunker.generateChunkingSummary(chunks);

    // Inform user about chunking
    const proceedWithChunking = await vscode.window.showInformationMessage(
      `Large diff detected! ${chunkingSummary}\n\nWould you like to proceed with chunked processing?`,
      { modal: true },
      "Proceed",
      "Cancel"
    );

    if (proceedWithChunking !== "Proceed") {
      return;
    }

    const generator = CommitGeneratorFactory.create(modelName, apiKey, {
      variant,
      baseUrl:
        modelName === "ollama" ? configManager.getOllamaBaseURL() : undefined,
    });
    const language = configManager.getWhisperLanguage();

    const individualMessages: string[] = [];
    let processedChunks = 0;

    // Process each chunk
    await vscode.window.withProgress(
      {
        location: vscode.ProgressLocation.Notification,
        title: `Processing chunk 1 of ${chunks.length}...`,
        cancellable: false,
      },
      async (progress) => {
        for (let i = 0; i < chunks.length; i++) {
          const chunk = chunks[i];

          progress.report({
            message: `Processing chunk ${i + 1} of ${
              chunks.length
            } (${chunk.files.join(", ")})...`,
            increment: 100 / chunks.length,
          });

          try {
            const chunkMessage = await generator.generateCommitMessage(
              chunk.content,
              language,
              prefix
            );

            const cleanedMessage =
              GitUtils.stripMarkdownCodeBlocks(chunkMessage);
            individualMessages.push(cleanedMessage);
            processedChunks++;
          } catch (error: any) {
            console.error(`Error processing chunk ${i + 1}:`, error);
            // Continue with other chunks
          }
        }
      }
    );

    if (individualMessages.length === 0) {
      vscode.window.showErrorMessage(
        "Failed to generate commit messages for any chunks."
      );
      return;
    }

    // Combine messages
    const combinedMessage = DiffChunker.combineCommitMessages(
      individualMessages,
      language
    );
    let finalMessage = autoFixCommitMessage(combinedMessage);

    // Validate the combined message
    const messageValidation =
      ValidationUtils.validateCommitMessage(finalMessage);
    if (!messageValidation.isValid) {
      ValidationUtils.showValidationError(
        messageValidation,
        "in combined commit message"
      );
      return;
    }

    // Show warning if any
    if (messageValidation.suggestion) {
      ValidationUtils.showValidationWarning(
        messageValidation,
        "Combined commit message"
      );
    }

    // Show chunking results and combined message
    const chunkDetails = individualMessages
      .map((msg, i) => `Chunk ${i + 1}: ${msg}`)
      .join("\n");
    const detailsMessage = `Processed ${processedChunks}/${chunks.length} chunks successfully.\n\nIndividual messages:\n${chunkDetails}\n\nCombined message:\n${finalMessage}`;

    // Check if this is a GitHub repository
    const isGitHub = await gitUtils.isGitHubRepository();

    // Show the generated message and ask for confirmation
    let userAction: string | undefined;

    do {
      const buttons = [
        "Commit",
        "Edit Message",
        "View Details",
        "Copy to Clipboard",
        "Cancel",
      ];
      if (isGitHub) {
        buttons.splice(1, 0, "Commit and Push"); // Insert after "Commit"
      }

      userAction = await vscode.window.showInformationMessage(
        `Generated combined commit message from ${chunks.length} chunks:\n\n${finalMessage}`,
        { modal: true },
        ...buttons
      );

      if (userAction === "View Details") {
        // Show detailed breakdown in a new document
        const doc = await vscode.workspace.openTextDocument({
          content: detailsMessage,
          language: "markdown",
        });
        await vscode.window.showTextDocument(doc);
        // Continue the loop to show the dialog again
      } else if (userAction === "Edit Message") {
        const editedMessage = await vscode.window.showInputBox({
          prompt: "Edit the combined commit message",
          value: finalMessage,
          placeHolder: "Enter your commit message",
          ignoreFocusOut: true,
          validateInput: (value) => {
            if (!value || value.trim().length === 0) {
              return "Commit message cannot be empty";
            }
            const validation = ValidationUtils.validateCommitMessage(
              value.trim()
            );
            if (!validation.isValid) {
              return validation.error;
            }
            return null;
          },
        });

        if (editedMessage !== undefined) {
          finalMessage = editedMessage.trim();
          // Validate the edited message
          const messageValidation =
            ValidationUtils.validateCommitMessage(finalMessage);
          if (messageValidation.suggestion) {
            ValidationUtils.showValidationWarning(
              messageValidation,
              "Edited commit message"
            );
          }
        }
        // Continue the loop to show the dialog again with the edited message
      }
    } while (userAction === "Edit Message" || userAction === "View Details");

    const action = userAction;

    if (action === "Commit") {
      const autoPush = configManager.shouldAutoPush();
      const githubPAT = autoPush
        ? await configManager.getGitHubPAT()
        : undefined;
      await gitUtils.runGitCommit({
        message: finalMessage,
        autoPush,
        githubPAT,
      });
      statusBarManager?.showSuccess("Chunked commit successful!");
    } else if (action === "Commit and Push") {
      await handleCommitAndPush(
        configManager,
        gitUtils,
        finalMessage,
        statusBarManager
      );
    } else if (action === "Copy to Clipboard") {
      await vscode.env.clipboard.writeText(finalMessage);
      vscode.window.showInformationMessage(
        "Combined commit message copied to clipboard!"
      );
      statusBarManager?.showSuccess("Copied to clipboard!");
    }
  } catch (error: any) {
    if (error instanceof ApiException) {
      ErrorHandler.handleErrorWithRetry(
        error,
        "generating chunked commit message"
      );

      if (ErrorHandler.shouldSuggestModelSwitch(error)) {
        const suggestions = ErrorHandler.getModelSwitchSuggestions(error);
        ErrorHandler.handleErrorWithFallback(
          error,
          "generating chunked commit message",
          suggestions
        );
      }
    } else {
      ErrorHandler.handleGeneralError(
        error,
        "generating chunked commit message"
      );
    }
    statusBarManager?.showError(error.message);
  }
}

/**
 * Analyze staged changes with AI
 */
async function analyzeChanges(
  configManager: ConfigManager,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    const workspaceFolder = getWorkspaceFolder();
    if (!workspaceFolder) {
      return;
    }

    const gitUtils = new GitUtils(workspaceFolder.uri.fsPath);

    // Check if we're in a git repository
    if (!(await gitUtils.isGitRepository())) {
      vscode.window.showErrorMessage(
        "No git repository found. Please run in a git repository."
      );
      return;
    }

    // Check for staged changes
    const hasStagedChanges = await gitUtils.hasStagedChanges();
    if (!hasStagedChanges) {
      // Check if there are unstaged changes
      const hasUnstagedChanges = await gitUtils.hasUnstagedChanges();
      if (hasUnstagedChanges) {
        const action = await vscode.window.showWarningMessage(
          "No staged changes found. Would you like to stage all changes and analyze them?",
          "Stage All & Analyze",
          "Stage Manually",
          "Cancel"
        );

        if (action === "Stage All & Analyze") {
          vscode.window.showInformationMessage("Staging all changes...");
          const stagedCount = await gitUtils.stageAllUnstagedFilesAndCount();
          vscode.window.showInformationMessage(`${stagedCount} files staged.`);
        } else if (action === "Stage Manually") {
          vscode.window.showInformationMessage(
            "Please stage your changes using `git add` and try again."
          );
          return;
        } else {
          return;
        }
      } else {
        vscode.window.showErrorMessage("No staged or unstaged changes found!");
        return;
      }
    }

    // Get model configuration
    const { modelName, variant, apiKey } = await getModelConfiguration(
      configManager
    );
    if (!modelName) {
      return;
    }

    // Get staged diff with file filtering
    const ignorePatterns = configManager.getIgnoredFiles();
    const diffResult = await gitUtils.getFilteredStagedDiff(ignorePatterns);
    const diff = diffResult.diff;

    if (!diff) {
      vscode.window.showErrorMessage("No changes detected in staged files.");
      return;
    }

    // Show filtering summary if files were ignored
    if (diffResult.ignoredFiles.length > 0) {
      const message = `Ignored ${
        diffResult.ignoredFiles.length
      } file(s): ${diffResult.ignoredFiles.slice(0, 3).join(", ")}${
        diffResult.ignoredFiles.length > 3
          ? ` and ${diffResult.ignoredFiles.length - 3} more`
          : ""
      }`;
      vscode.window.showInformationMessage(message);
    }

    // Analyze changes
    vscode.window.withProgress(
      {
        location: vscode.ProgressLocation.Notification,
        title: `Analyzing changes using ${modelName}...`,
        cancellable: false,
      },
      async () => {
        try {
          const generator = CommitGeneratorFactory.create(modelName, apiKey, {
            variant,
            baseUrl:
              modelName === "ollama"
                ? configManager.getOllamaBaseURL()
                : undefined,
          });
          const language = configManager.getWhisperLanguage();

          const analysis = await generator.analyzeChanges(diff, language);

          // Show analysis in a new document
          const doc = await vscode.workspace.openTextDocument({
            content: analysis,
            language: "markdown",
          });
          await vscode.window.showTextDocument(doc);
        } catch (error: any) {
          if (error instanceof ApiException) {
            ErrorHandler.handleErrorWithRetry(error, "analyzing changes");

            if (ErrorHandler.shouldSuggestModelSwitch(error)) {
              const suggestions = ErrorHandler.getModelSwitchSuggestions(error);
              ErrorHandler.handleErrorWithFallback(
                error,
                "analyzing changes",
                suggestions
              );
            }
          } else {
            ErrorHandler.handleGeneralError(error, "analyzing changes");
          }
          statusBarManager?.showError(error.message);
        }
      }
    );
  } catch (error: any) {
    vscode.window.showErrorMessage(`Error: ${error.message}`);
  }
}

/**
 * Show configuration panel
 */
async function showConfiguration(
  configManager: ConfigManager,
  context?: vscode.ExtensionContext
): Promise<void> {
  try {
    if (context) {
      // Show webview settings panel
      SettingsPanel.createOrShow(context.extensionUri, configManager);
    } else {
      // Fallback to text-based configuration
      const config = await configManager.getConfigSummary();

      const items = [
        `Default Model: ${config.defaultModel || "Not set"}`,
        `Default Variant: ${config.defaultVariant || "Not set"}`,
        `Language: ${config.language.displayName}`,
        `Always Add: ${config.alwaysAdd ? "Enabled" : "Disabled"}`,
        `Auto Push: ${config.autoPush ? "Enabled" : "Disabled"}`,
        `Ollama Base URL: ${config.ollamaBaseUrl}`,
        "",
        "API Keys Status:",
        ...Object.entries(config.hasApiKeys).map(
          ([model, hasKey]) => `  ${model}: ${hasKey ? "✓ Set" : "✗ Not set"}`
        ),
      ];

      const content = items.join("\n");

      const doc = await vscode.workspace.openTextDocument({
        content: `# GitWhisper Configuration\n\n${content}`,
        language: "markdown",
      });
      await vscode.window.showTextDocument(doc);
    }
  } catch (error: any) {
    vscode.window.showErrorMessage(
      `Error showing configuration: ${error.message}`
    );
  }
}

/**
 * Set API key for a model
 */
async function setApiKey(
  configManager: ConfigManager,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    const allModels = CommitGeneratorFactory.getAvailableModels();
    const implementedModels = CommitGeneratorFactory.getImplementedModels();
    const customOllamaVariant = configManager.getCustomOllamaVariant();

    const modelItems = allModels.map((model) => {
      let description = implementedModels.includes(model)
        ? "✓ Available"
        : "⚠️ Coming Soon";

      // Add custom variant info for Ollama
      if (
        model === "ollama" &&
        customOllamaVariant &&
        customOllamaVariant.trim()
      ) {
        description = `✓ Available (Custom: ${customOllamaVariant.trim()})`;
      }

      return {
        label: model,
        description,
        detail: implementedModels.includes(model)
          ? undefined
          : "This model is not yet implemented. Only OpenAI and Claude are currently available.",
      };
    });

    const selectedModelItem = await vscode.window.showQuickPick(modelItems, {
      placeHolder: "Select a model to set API key for",
    });

    if (!selectedModelItem) {
      return;
    }

    // Check if the selected model is implemented
    if (!implementedModels.includes(selectedModelItem.label)) {
      vscode.window.showWarningMessage(
        `${selectedModelItem.label} is not yet implemented. Please choose OpenAI or Claude.`
      );
      return;
    }

    const apiKey = await vscode.window.showInputBox({
      prompt: `Enter API key for ${selectedModelItem.label}`,
      password: true,
      placeHolder: "Your API key will be stored securely",
    });

    if (apiKey) {
      // Validate API key format
      const validation = ValidationUtils.validateApiKey(
        apiKey,
        selectedModelItem.label
      );
      if (!validation.isValid) {
        ValidationUtils.showValidationError(
          validation,
          `for ${selectedModelItem.label}`
        );
        return;
      }

      await configManager.setApiKey(selectedModelItem.label, apiKey);
      statusBarManager?.updateStatusBar();
    }
  } catch (error: any) {
    vscode.window.showErrorMessage(`Error setting API key: ${error.message}`);
  }
}

/**
 * Select default model and variant
 */
async function selectModel(
  configManager: ConfigManager,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    const allModels = CommitGeneratorFactory.getAvailableModels();
    const implementedModels = CommitGeneratorFactory.getImplementedModels();
    const customOllamaVariant = configManager.getCustomOllamaVariant();

    const modelItems = allModels.map((model) => {
      let description = implementedModels.includes(model)
        ? "✓ Available"
        : "⚠️ Coming Soon";

      // Add custom variant info for Ollama
      if (
        model === "ollama" &&
        customOllamaVariant &&
        customOllamaVariant.trim()
      ) {
        description = `✓ Available (Custom: ${customOllamaVariant.trim()})`;
      }

      return {
        label: model,
        description,
        detail: implementedModels.includes(model)
          ? undefined
          : "This model is not yet implemented. Only OpenAI and Claude are currently available.",
      };
    });

    const selectedModel = await vscode.window.showQuickPick(modelItems, {
      placeHolder: "Select default AI model",
    });

    if (!selectedModel) {
      return;
    }

    // Check if the selected model is implemented
    if (!implementedModels.includes(selectedModel.label)) {
      vscode.window.showWarningMessage(
        `${selectedModel.label} is not yet implemented. Please choose OpenAI or Claude.`
      );
      return;
    }

    const variants = getVariantsWithCustom(
      selectedModel.label,
      customOllamaVariant
    );
    const variantItems = variants.map((v) => ({
      label: v.displayName,
      description: v.description,
      detail: v.name,
    }));

    const selectedVariant = await vscode.window.showQuickPick(variantItems, {
      placeHolder: `Select variant for ${selectedModel.label}`,
    });

    if (selectedVariant) {
      await configManager.setDefaults(
        selectedModel.label,
        selectedVariant.detail
      );
      statusBarManager?.updateStatusBar();
    }
  } catch (error: any) {
    vscode.window.showErrorMessage(`Error selecting model: ${error.message}`);
  }
}

/**
 * Set commit message language
 */
async function setLanguage(
  configManager: ConfigManager,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    const languages = getAllLanguages();
    const languageItems = languages.map((lang) => ({
      label: lang.displayName,
      description: `${lang.code}-${lang.countryCode}`,
      detail: lang.name,
    }));

    const selectedLanguage = await vscode.window.showQuickPick(languageItems, {
      placeHolder: "Select language for commit messages",
    });

    if (selectedLanguage) {
      const language = languages.find(
        (l) => l.displayName === selectedLanguage.label
      );
      if (language) {
        await configManager.setWhisperLanguage(language);
        statusBarManager?.updateStatusBar();
      }
    }
  } catch (error: any) {
    vscode.window.showErrorMessage(`Error setting language: ${error.message}`);
  }
}

/**
 * Get workspace folder
 */
function getWorkspaceFolder(): vscode.WorkspaceFolder | undefined {
  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (!workspaceFolders || workspaceFolders.length === 0) {
    vscode.window.showErrorMessage(
      "No workspace folder found. Please open a folder first."
    );
    return undefined;
  }
  return workspaceFolders[0];
}

/**
 * Get model configuration (model, variant, API key)
 */
async function getModelConfiguration(configManager: ConfigManager): Promise<{
  modelName: string | undefined;
  variant: string | undefined;
  apiKey: string | undefined;
}> {
  // Get default model and variant from config
  const defaults = configManager.getDefaultModelAndVariant();
  let modelName = defaults?.[0];
  let variant = defaults?.[1];

  // If no defaults, prompt user to select
  if (!modelName) {
    const allModels = CommitGeneratorFactory.getAvailableModels();
    const implementedModels = CommitGeneratorFactory.getImplementedModels();
    const customOllamaVariant = configManager.getCustomOllamaVariant();

    const modelItems = allModels.map((model) => {
      let description = implementedModels.includes(model)
        ? "✓ Available"
        : "⚠️ Coming Soon";

      // Add custom variant info for Ollama
      if (
        model === "ollama" &&
        customOllamaVariant &&
        customOllamaVariant.trim()
      ) {
        description = `✓ Available (Custom: ${customOllamaVariant.trim()})`;
      }

      return {
        label: model,
        description,
        detail: implementedModels.includes(model)
          ? undefined
          : "This model is not yet implemented. Only OpenAI and Claude are currently available.",
      };
    });

    const selectedModelItem = await vscode.window.showQuickPick(modelItems, {
      placeHolder:
        "Select AI model (set default in settings to skip this step)",
    });

    if (!selectedModelItem) {
      return { modelName: undefined, variant: undefined, apiKey: undefined };
    }

    modelName = selectedModelItem.label;

    // Check if the selected model is implemented
    if (!implementedModels.includes(modelName)) {
      vscode.window.showWarningMessage(
        `${modelName} is not yet implemented. Please choose OpenAI or Claude.`
      );
      return { modelName: undefined, variant: undefined, apiKey: undefined };
    }

    variant = getDefaultVariant(modelName);
  }

  // For Ollama, check if there's a custom variant configured
  if (modelName === "ollama") {
    const customVariant = configManager.getCustomOllamaVariant();
    console.log(
      `[GitWhisper] Custom Ollama variant configured: ${customVariant}`
    );
    if (customVariant && customVariant.trim()) {
      variant = customVariant.trim();
      console.log(`[GitWhisper] Using custom Ollama variant: ${variant}`);
    }
  }

  // Get API key
  let apiKey = await configManager.getApiKey(modelName);
  if (!apiKey) {
    apiKey = configManager.getEnvironmentApiKey(modelName);
  }

  // If no API key and model requires one, prompt user
  const requirements = CommitGeneratorFactory.getModelRequirements(modelName);
  if (!apiKey && requirements.requiresApiKey) {
    const action = await vscode.window.showErrorMessage(
      `No API key found for ${modelName}. Please set one first.`,
      "Set API Key",
      "Cancel"
    );

    if (action === "Set API Key") {
      const inputKey = await vscode.window.showInputBox({
        prompt: `Enter API key for ${modelName}`,
        password: true,
        placeHolder: "Your API key will be stored securely",
      });

      if (inputKey) {
        await configManager.setApiKey(modelName, inputKey);
        apiKey = inputKey;
      }
    }

    if (!apiKey) {
      return { modelName: undefined, variant: undefined, apiKey: undefined };
    }
  }

  // For models that don't require API key (like Ollama), ensure we still have a value
  if (!requirements.requiresApiKey && !apiKey) {
    apiKey = ""; // Set empty string for models that don't need API key
    console.log(
      `[GitWhisper] Model ${modelName} doesn't require API key, using empty string`
    );
  }

  console.log(
    `[GitWhisper] Final configuration - Model: ${modelName}, Variant: ${variant}, HasApiKey: ${
      apiKey ? "Yes" : "No"
    }`
  );
  return { modelName, variant, apiKey };
}

/**
 * Handle commit and push with GitHub PAT authentication
 */
async function handleCommitAndPush(
  configManager: ConfigManager,
  gitUtils: GitUtils,
  message: string,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    // Check if we have a GitHub PAT
    let githubPAT = await configManager.getGitHubPAT();

    if (!githubPAT) {
      // Prompt user to set up GitHub PAT
      const setupPAT = await vscode.window.showInformationMessage(
        "GitHub Personal Access Token is required for pushing to private repositories. Would you like to set it up now?",
        { modal: true },
        "Set up PAT",
        "Continue without PAT",
        "Cancel"
      );

      if (setupPAT === "Set up PAT") {
        await setGitHubPAT(configManager, statusBarManager);
        githubPAT = await configManager.getGitHubPAT();

        if (!githubPAT) {
          vscode.window.showErrorMessage("GitHub PAT setup was cancelled.");
          return;
        }
      } else if (setupPAT === "Cancel") {
        return;
      }
      // If "Continue without PAT", githubPAT remains undefined
    }

    // Perform commit and push
    await gitUtils.runGitCommitAndPush({
      message,
      githubPAT,
    });
    statusBarManager?.showSuccess("Commit and push successful!");
  } catch (error: any) {
    console.error("Error during commit and push:", error);
    vscode.window.showErrorMessage(`Commit and push failed: ${error.message}`);
    statusBarManager?.showError(error.message);
  }
}

/**
 * Set GitHub Personal Access Token
 */
async function setGitHubPAT(
  configManager: ConfigManager,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    const currentPAT = await configManager.getGitHubPAT();
    const placeholder = currentPAT
      ? "Enter new GitHub PAT (current one will be replaced)"
      : "Enter your GitHub Personal Access Token";

    const pat = await vscode.window.showInputBox({
      prompt: "GitHub Personal Access Token",
      placeHolder: placeholder,
      password: true,
      ignoreFocusOut: true,
      validateInput: (value) => {
        if (!value || value.trim().length === 0) {
          return "GitHub PAT cannot be empty";
        }
        if (value.length < 10) {
          return "GitHub PAT seems too short";
        }
        return null;
      },
    });

    if (pat) {
      await configManager.setGitHubPAT(pat.trim());
      statusBarManager?.showSuccess("GitHub PAT saved!");

      // Show instructions
      vscode.window
        .showInformationMessage(
          "GitHub PAT saved! You can now use 'Commit and Push' for private repositories. " +
            "Make sure your PAT has 'repo' permissions (Repository Permissions/Contents set to read and write) if you want to push using GitWhisper.",
          "Learn More"
        )
        .then((selection) => {
          if (selection === "Learn More") {
            vscode.env.openExternal(
              vscode.Uri.parse(
                "https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens"
              )
            );
          }
        });
    }
  } catch (error: any) {
    console.error("Error setting GitHub PAT:", error);
    vscode.window.showErrorMessage(
      `Failed to set GitHub PAT: ${error.message}`
    );
    statusBarManager?.showError(error.message);
  }
}

/**
 * Set custom Ollama variant
 */
async function setCustomOllamaVariant(
  configManager: ConfigManager,
  statusBarManager?: StatusBarManager
): Promise<void> {
  try {
    const currentVariant = configManager.getCustomOllamaVariant();
    const placeholder = currentVariant
      ? `Current: ${currentVariant} (enter new variant or leave empty to clear)`
      : "Enter Ollama model name (e.g., deepseek-r1:1.5b, llama3.2:1b)";

    const variant = await vscode.window.showInputBox({
      prompt: "Custom Ollama Model Variant",
      placeHolder: placeholder,
      value: currentVariant || "",
      ignoreFocusOut: true,
      validateInput: (value) => {
        if (value && value.trim() && value.includes(" ")) {
          return "Model name should not contain spaces";
        }
        return null;
      },
    });

    if (variant !== undefined) {
      if (variant.trim()) {
        await configManager.setCustomOllamaVariant(variant.trim());
        statusBarManager?.showSuccess(
          `Custom Ollama variant set to: ${variant.trim()}`
        );

        // Ask if user wants to set this as default
        const setAsDefault = await vscode.window.showInformationMessage(
          `Custom Ollama variant "${variant.trim()}" has been set. Would you like to make Ollama your default model?`,
          "Set as Default",
          "Not Now"
        );

        if (setAsDefault === "Set as Default") {
          await configManager.setDefaults("ollama", variant.trim());
          statusBarManager?.showSuccess("Ollama set as default model!");
        }
      } else {
        await configManager.setCustomOllamaVariant("");
        statusBarManager?.showSuccess("Custom Ollama variant cleared");
      }
      statusBarManager?.updateStatusBar();
    }
  } catch (error: any) {
    console.error("Error setting custom Ollama variant:", error);
    vscode.window.showErrorMessage(
      `Failed to set custom Ollama variant: ${error.message}`
    );
    statusBarManager?.showError(error.message);
  }
}

/**
 * Auto-fix commit message length and format issues
 */
function autoFixCommitMessage(message: string): string {
  const trimmed = message.trim();
  const lines = trimmed.split("\n");
  const firstLine = lines[0];

  // If first line is too long, truncate it intelligently
  if (firstLine.length > 72) {
    console.log(
      `[GitWhisper] Auto-fixing long commit message (${firstLine.length} chars)`
    );

    // Try to find conventional commit pattern
    const match = firstLine.match(/^([^:]+:\s*[^\s]+\s*)(.*)$/);
    if (match) {
      const typeAndEmoji = match[1]; // "feat: ✨ "
      const description = match[2];

      const availableLength = 72 - typeAndEmoji.length;
      if (availableLength > 10) {
        // Truncate description intelligently
        let truncatedDesc = description.substring(0, availableLength - 3);

        // Try to break at word boundary
        const lastSpace = truncatedDesc.lastIndexOf(" ");
        if (lastSpace > availableLength / 2) {
          truncatedDesc = truncatedDesc.substring(0, lastSpace);
        }

        const fixedFirstLine = typeAndEmoji + truncatedDesc + "...";
        console.log(`[GitWhisper] Fixed to: ${fixedFirstLine}`);

        // Reconstruct message with fixed first line
        return [fixedFirstLine, ...lines.slice(1)].join("\n");
      }
    }

    // Fallback: simple truncation
    const fixedFirstLine = firstLine.substring(0, 69) + "...";
    console.log(`[GitWhisper] Fixed to: ${fixedFirstLine}`);
    return [fixedFirstLine, ...lines.slice(1)].join("\n");
  }

  return trimmed;
}

export function deactivate() {
  // Clean up any resources if needed
}
