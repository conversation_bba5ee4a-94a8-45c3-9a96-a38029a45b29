import { Language } from "./language";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ApiException } from "../utils/errorHandler";

/**
 * Abstract base class for AI commit message generators
 * Based on the CommitGenerator from the reference Dart implementation
 */
export abstract class CommitGenerator {
  protected apiKey: string | undefined;
  protected variant: string | undefined;

  constructor(apiKey: string | undefined, variant?: string) {
    this.apiKey = apiKey;
    this.variant = variant;
  }

  /**
   * Generate a commit message based on the git diff
   */
  abstract generateCommitMessage(
    diff: string,
    language: Language,
    prefix?: string
  ): Promise<string>;

  /**
   * Generate an analysis of the provided diff for what's changed and possibly
   * what can be made better
   */
  abstract analyzeChanges(diff: string, language: Language): Promise<string>;

  /**
   * Returns the name of the model
   */
  abstract get modelName(): string;

  /**
   * Returns the default variant to use if none specified
   */
  abstract get defaultVariant(): string;

  /**
   * Gets the actual variant to use (specified or default)
   */
  get actualVariant(): string {
    return this.variant && this.variant.length > 0
      ? this.variant
      : this.defaultVariant;
  }

  /**
   * The maximum number of tokens allowed for the commit message generation.
   *
   * This limits the size of the generated commit message to ensure it remains
   * concise and follows best practices for Git commit messages.
   * A lower value encourages more focused, single-purpose commit messages.
   */
  get maxTokens(): number {
    return 300;
  }

  /**
   * The maximum number of tokens allowed for the analysis message generation.
   */
  get maxAnalysisTokens(): number {
    return 8000;
  }

  /**
   * Validate that the generator has the necessary configuration
   */
  protected validateConfiguration(): void {
    if (!this.apiKey && this.modelName !== "ollama") {
      throw new Error(`No API key provided for ${this.modelName}`);
    }
  }

  /**
   * Handle API errors with appropriate error messages
   */
  protected handleApiError(error: any, context: string): never {
    console.error(`${this.modelName} API error in ${context}:`, error);

    // Use the new error handler to parse and throw appropriate exception
    const apiException = ErrorHandler.parseHttpError(error, this.modelName);
    throw apiException;
  }

  /**
   * Clean up the generated response by removing markdown code blocks
   */
  protected cleanResponse(response: string): string {
    // Remove markdown code blocks
    const codeBlockPattern = /^```(\w+)?\n?|```$/gm;
    return response.replace(codeBlockPattern, "").trim();
  }

  /**
   * Validate the generated commit message
   */
  protected validateCommitMessage(message: string): void {
    if (!message || message.trim().length === 0) {
      throw new Error("Generated commit message is empty");
    }

    const firstLine = message.trim().split("\n")[0];

    // Check for length constraint
    if (firstLine.length > 72) {
      console.warn(
        `Generated commit message is too long (${firstLine.length} chars): ${firstLine}`
      );
      // Don't throw error, let the validation in extension handle it
    }

    // Check for basic conventional commit format
    const conventionalCommitPattern =
      /^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert):\s*[✨🐛📚💄♻️🧪🔧⚡👷📦⏪]\s*.+/;
    if (!conventionalCommitPattern.test(message.trim())) {
      console.warn(
        "Generated commit message may not follow conventional commit format:",
        message
      );
    }
  }

  /**
   * Get common headers for API requests
   */
  protected getCommonHeaders(): { [key: string]: string } {
    return {
      "Content-Type": "application/json",
      "User-Agent": "GitWhisper-VSCode/1.0.0",
    };
  }

  /**
   * Get timeout configuration for API requests
   */
  protected getTimeoutConfig(): number {
    return 30000; // 30 seconds
  }
}
