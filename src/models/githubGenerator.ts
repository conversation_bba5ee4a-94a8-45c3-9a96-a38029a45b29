import axios, { AxiosResponse } from "axios";
import { CommitGenerator } from "./commitGenerator";
import { Language } from "./language";
import { getCommitPrompt, getAnalysisPrompt } from "../utils/commitUtils";
import { getDefaultVariant } from "./modelVariants";
import { <PERSON>rror<PERSON>and<PERSON> } from "../utils/errorHandler";

/**
 * GitHub Models commit message generator
 * Based on the GithubGenerator from the reference Dart implementation
 *
 * GitHub Models provides access to various AI models through Azure AI
 * Uses GitHub Personal Access Token (PAT) for authentication
 */
export class GitHubGenerator extends CommitGenerator {
  constructor(pat: string, variant?: string) {
    super(pat, variant);
  }

  get modelName(): string {
    return "github";
  }

  get defaultVariant(): string {
    return getDefaultVariant(this.modelName);
  }

  async generateCommitMessage(
    diff: string,
    language: Language,
    prefix?: string
  ): Promise<string> {
    const prompt = getCommitPrompt(diff, language, prefix);

    try {
      const response: AxiosResponse = await axios.post(
        "https://models.inference.ai.azure.com/chat/completions",
        {
          model: this.actualVariant,
          store: true,
          messages: [{ role: "user", content: prompt }],
          max_tokens: this.maxTokens,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.apiKey}`, // Using PAT as Bearer token
          },
        }
      );

      if (response.status === 200) {
        return response.data.choices[0].message.content.trim();
      } else {
        throw new Error(
          `Unexpected response from GitHub API: ${response.status}`
        );
      }
    } catch (error: any) {
      throw ErrorHandler.parseHttpError(error, "github");
    }
  }

  async analyzeChanges(diff: string, language: Language): Promise<string> {
    const prompt = getAnalysisPrompt(diff, language);

    try {
      const response: AxiosResponse = await axios.post(
        "https://models.inference.ai.azure.com/chat/completions",
        {
          model: this.actualVariant,
          store: true,
          messages: [{ role: "user", content: prompt }],
          max_tokens: this.maxAnalysisTokens,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.apiKey}`, // Using PAT as Bearer token
          },
        }
      );

      if (response.status === 200) {
        return response.data.choices[0].message.content.trim();
      } else {
        throw new Error(
          `Unexpected response from GitHub API: ${response.status}`
        );
      }
    } catch (error: any) {
      throw ErrorHandler.parseHttpError(error, "github");
    }
  }
}
