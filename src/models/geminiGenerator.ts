import axios, { AxiosResponse } from 'axios';
import { CommitGenerator } from './commitGenerator';
import { Language } from './language';
import { getCommitPrompt, getAnalysisPrompt } from '../utils/commitUtils';
import { getDefaultVariant } from './modelVariants';
import { ErrorHandler } from '../utils/errorHandler';

/**
 * Google Gemini commit message generator
 * Based on the GeminiGenerator from the reference Dart implementation
 */
export class GeminiGenerator extends CommitGenerator {
    constructor(apiKey: string, variant?: string) {
        super(apiKey, variant);
    }

    get modelName(): string {
        return 'gemini';
    }

    get defaultVariant(): string {
        return getDefaultVariant(this.modelName);
    }

    async generateCommitMessage(
        diff: string,
        language: Language,
        prefix?: string
    ): Promise<string> {
        const prompt = getCommitPrompt(diff, language, prefix);

        try {
            const response: AxiosResponse = await axios.post(
                `https://generativelanguage.googleapis.com/v1beta/models/${this.actualVariant}:generateContent?key=${this.apiKey}`,
                {
                    contents: [
                        {
                            parts: [
                                { text: prompt }
                            ]
                        }
                    ],
                    generationConfig: {
                        maxOutputTokens: this.maxTokens
                    }
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.status === 200) {
                return response.data.candidates[0].content.parts[0].text.trim();
            } else {
                throw new Error(`Unexpected response from Gemini API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'gemini');
        }
    }

    async analyzeChanges(diff: string, language: Language): Promise<string> {
        const prompt = getAnalysisPrompt(diff, language);

        try {
            const response: AxiosResponse = await axios.post(
                `https://generativelanguage.googleapis.com/v1beta/models/${this.actualVariant}:generateContent?key=${this.apiKey}`,
                {
                    contents: [
                        {
                            parts: [
                                { text: prompt }
                            ]
                        }
                    ],
                    generationConfig: {
                        maxOutputTokens: this.maxAnalysisTokens
                    }
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.status === 200) {
                return response.data.candidates[0].content.parts[0].text.trim();
            } else {
                throw new Error(`Unexpected response from Gemini API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'gemini');
        }
    }
}
