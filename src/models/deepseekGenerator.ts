import axios, { AxiosResponse } from 'axios';
import { CommitGenerator } from './commitGenerator';
import { Language } from './language';
import { getCommitPrompt, getAnalysisPrompt } from '../utils/commitUtils';
import { getDefaultVariant } from './modelVariants';
import { ErrorHandler } from '../utils/errorHandler';

/**
 * DeepSeek commit message generator
 * Based on the DeepseekGenerator from the reference Dart implementation
 */
export class DeepSeekGenerator extends CommitGenerator {
    constructor(apiKey: string, variant?: string) {
        super(apiKey, variant);
    }

    get modelName(): string {
        return 'deepseek';
    }

    get defaultVariant(): string {
        return getDefaultVariant(this.modelName);
    }

    async generateCommitMessage(
        diff: string,
        language: Language,
        prefix?: string
    ): Promise<string> {
        const prompt = getCommitPrompt(diff, language, prefix);

        try {
            const response: AxiosResponse = await axios.post(
                'https://api.deepseek.com/v1/chat/completions',
                {
                    model: this.actualVariant,
                    store: true,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    max_tokens: this.maxTokens
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    }
                }
            );

            if (response.status === 200) {
                return response.data.choices[0].message.content.trim();
            } else {
                throw new Error(`Unexpected response from DeepSeek API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'deepseek');
        }
    }

    async analyzeChanges(diff: string, language: Language): Promise<string> {
        const prompt = getAnalysisPrompt(diff, language);

        try {
            const response: AxiosResponse = await axios.post(
                'https://api.deepseek.com/v1/chat/completions',
                {
                    model: this.actualVariant,
                    store: true,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    max_tokens: this.maxAnalysisTokens
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    }
                }
            );

            if (response.status === 200) {
                return response.data.choices[0].message.content.trim();
            } else {
                throw new Error(`Unexpected response from DeepSeek API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'deepseek');
        }
    }
}
