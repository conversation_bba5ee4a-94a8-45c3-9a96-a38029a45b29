import { CommitGenerator } from "./commitGenerator";
import { OpenAIGenerator } from "./openaiGenerator";
import { ClaudeGenerator } from "./claudeGenerator";
import { GeminiGenerator } from "./geminiGenerator";
import { GrokGenerator } from "./grokGenerator";
import { LlamaGenerator } from "./llamaGenerator";
import { DeepSeekGenerator } from "./deepseekGenerator";
import { GitHubGenerator } from "./githubGenerator";
import { OllamaGenerator } from "./ollamaGenerator";
import { isModelSupported } from "./modelVariants";

/**
 * Factory for creating commit generators based on model name
 * Based on the CommitGeneratorFactory from the reference Dart implementation
 */
export class CommitGeneratorFactory {
  /**
   * Create a commit generator for the specified model
   */
  static create(
    modelName: string,
    apiKey: string | undefined,
    options?: {
      variant?: string;
      baseUrl?: string;
    }
  ): CommitGenerator {
    if (!isModelSupported(modelName)) {
      throw new Error(`Unsupported model: ${modelName}`);
    }

    // Check if API key is required for this model
    const requirements = this.getModelRequirements(modelName);
    if (requirements.requiresApiKey && !apiKey) {
      throw new Error(`API key is required for ${modelName} model`);
    }

    const variant = options?.variant;
    const baseUrl = options?.baseUrl;

    switch (modelName.toLowerCase()) {
      case "openai":
        return new OpenAIGenerator(apiKey!, variant);

      case "claude":
        return new ClaudeGenerator(apiKey!, variant);

      case "gemini":
        return new GeminiGenerator(apiKey!, variant);

      case "grok":
        return new GrokGenerator(apiKey!, variant);

      case "llama":
        return new LlamaGenerator(apiKey!, variant);

      case "deepseek":
        return new DeepSeekGenerator(apiKey!, variant);

      case "github":
        return new GitHubGenerator(apiKey!, variant);

      case "ollama":
        return new OllamaGenerator(apiKey || "", variant, baseUrl);

      default:
        throw new Error(`Unknown model: ${modelName}`);
    }
  }

  /**
   * Get list of available models
   */
  static getAvailableModels(): string[] {
    // Return all supported models, even if some are not fully implemented
    return [
      "openai",
      "claude",
      "gemini",
      "grok",
      "llama",
      "deepseek",
      "github",
      "ollama",
    ];
  }

  /**
   * Check if a model is available (implemented)
   */
  static isModelAvailable(modelName: string): boolean {
    return this.getAvailableModels().includes(modelName.toLowerCase());
  }

  /**
   * Check if a model is fully implemented
   */
  static isModelImplemented(modelName: string): boolean {
    return [
      "openai",
      "claude",
      "gemini",
      "grok",
      "llama",
      "deepseek",
      "github",
      "ollama",
    ].includes(modelName.toLowerCase());
  }

  /**
   * Get list of fully implemented models
   */
  static getImplementedModels(): string[] {
    return [
      "openai",
      "claude",
      "gemini",
      "grok",
      "llama",
      "deepseek",
      "github",
      "ollama",
    ];
  }

  /**
   * Get model requirements (whether API key is needed)
   */
  static getModelRequirements(modelName: string): {
    requiresApiKey: boolean;
    requiresBaseUrl: boolean;
    description: string;
  } {
    switch (modelName.toLowerCase()) {
      case "openai":
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Requires OpenAI API key",
        };

      case "claude":
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Requires Anthropic API key",
        };

      case "gemini":
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Requires Google AI API key",
        };

      case "grok":
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Requires xAI API key",
        };

      case "llama":
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Requires Meta API key",
        };

      case "deepseek":
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Requires DeepSeek API key",
        };

      case "github":
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Requires GitHub token",
        };

      case "ollama":
        return {
          requiresApiKey: false,
          requiresBaseUrl: true,
          description: "Requires local Ollama installation",
        };

      default:
        return {
          requiresApiKey: true,
          requiresBaseUrl: false,
          description: "Unknown model requirements",
        };
    }
  }
}
