/**
 * Model variants configuration for different AI providers
 * Based on the ModelVariants from the reference Dart implementation
 */

export interface ModelVariant {
  name: string;
  displayName: string;
  description?: string;
}

export const ModelVariants = {
  openai: [
    {
      name: "gpt-4o",
      displayName: "GPT-4o",
      description: "Latest GPT-4 Omni model",
    },
    {
      name: "gpt-4o-mini",
      displayName: "GPT-4o Mini",
      description: "Smaller, faster GPT-4o",
    },
    {
      name: "gpt-4-turbo",
      displayName: "GPT-4 Turbo",
      description: "GPT-4 Turbo with vision",
    },
    {
      name: "gpt-4",
      displayName: "GPT-4",
      description: "Original GPT-4 model",
    },
    {
      name: "gpt-3.5-turbo",
      displayName: "GPT-3.5 Turbo",
      description: "Fast and efficient",
    },
  ] as ModelVariant[],

  claude: [
    {
      name: "claude-3-5-sonnet-20241022",
      displayName: "Claude 3.5 Sonnet",
      description: "Latest Claude 3.5 Sonnet",
    },
    {
      name: "claude-3-5-haiku-20241022",
      displayName: "Claude 3.5 Haiku",
      description: "Fast and efficient Claude",
    },
    {
      name: "claude-3-opus-20240229",
      displayName: "Claude 3 Opus",
      description: "Most capable Claude model",
    },
    {
      name: "claude-3-sonnet-20240229",
      displayName: "Claude 3 Sonnet",
      description: "Balanced performance",
    },
    {
      name: "claude-3-haiku-20240307",
      displayName: "Claude 3 Haiku",
      description: "Fast and lightweight",
    },
  ] as ModelVariant[],

  gemini: [
    {
      name: "gemini-1.5-pro",
      displayName: "Gemini 1.5 Pro",
      description: "Latest Gemini Pro model",
    },
    {
      name: "gemini-1.5-flash",
      displayName: "Gemini 1.5 Flash",
      description: "Fast Gemini model",
    },
    {
      name: "gemini-pro",
      displayName: "Gemini Pro",
      description: "Standard Gemini Pro",
    },
    {
      name: "gemini-pro-vision",
      displayName: "Gemini Pro Vision",
      description: "Gemini with vision capabilities",
    },
  ] as ModelVariant[],

  grok: [
    {
      name: "grok-beta",
      displayName: "Grok Beta",
      description: "xAI Grok model",
    },
    {
      name: "grok-vision-beta",
      displayName: "Grok Vision Beta",
      description: "Grok with vision capabilities",
    },
  ] as ModelVariant[],

  llama: [
    {
      name: "llama-3.2-90b-text-preview",
      displayName: "Llama 3.2 90B",
      description: "Large Llama 3.2 model",
    },
    {
      name: "llama-3.2-11b-text-preview",
      displayName: "Llama 3.2 11B",
      description: "Medium Llama 3.2 model",
    },
    {
      name: "llama-3.1-70b-instruct",
      displayName: "Llama 3.1 70B",
      description: "Llama 3.1 70B Instruct",
    },
    {
      name: "llama-3.1-8b-instruct",
      displayName: "Llama 3.1 8B",
      description: "Llama 3.1 8B Instruct",
    },
  ] as ModelVariant[],

  deepseek: [
    {
      name: "deepseek-chat",
      displayName: "DeepSeek Chat",
      description: "DeepSeek conversational model",
    },
    {
      name: "deepseek-coder",
      displayName: "DeepSeek Coder",
      description: "DeepSeek specialized for coding",
    },
  ] as ModelVariant[],

  github: [
    {
      name: "gpt-4o",
      displayName: "GPT-4o (GitHub)",
      description: "GPT-4o via GitHub Models",
    },
    {
      name: "gpt-4o-mini",
      displayName: "GPT-4o Mini (GitHub)",
      description: "GPT-4o Mini via GitHub Models",
    },
    {
      name: "o1-preview",
      displayName: "o1 Preview (GitHub)",
      description: "OpenAI o1 Preview via GitHub",
    },
    {
      name: "o1-mini",
      displayName: "o1 Mini (GitHub)",
      description: "OpenAI o1 Mini via GitHub",
    },
  ] as ModelVariant[],

  ollama: [
    {
      name: "llama3.2",
      displayName: "Llama 3.2",
      description: "Meta Llama 3.2",
    },
    {
      name: "llama3.1",
      displayName: "Llama 3.1",
      description: "Meta Llama 3.1",
    },
    {
      name: "codellama",
      displayName: "Code Llama",
      description: "Code-specialized Llama",
    },
    {
      name: "mistral",
      displayName: "Mistral",
      description: "Mistral AI model",
    },
    {
      name: "mixtral",
      displayName: "Mixtral",
      description: "Mixtral mixture of experts",
    },
    {
      name: "qwen2.5",
      displayName: "Qwen 2.5",
      description: "Alibaba Qwen 2.5",
    },
    {
      name: "deepseek-coder",
      displayName: "DeepSeek Coder",
      description: "DeepSeek Coder via Ollama",
    },
    { name: "phi3", displayName: "Phi-3", description: "Microsoft Phi-3" },
    { name: "gemma2", displayName: "Gemma 2", description: "Google Gemma 2" },
  ] as ModelVariant[],
};

/**
 * Get default variant for a model
 */
export function getDefaultVariant(modelName: string): string {
  const variants = ModelVariants[modelName as keyof typeof ModelVariants];
  if (variants && variants.length > 0) {
    return variants[0].name;
  }

  // Fallback defaults
  const fallbacks: { [key: string]: string } = {
    openai: "gpt-4o",
    claude: "claude-3-5-sonnet-20241022",
    gemini: "gemini-1.5-pro",
    grok: "grok-beta",
    llama: "llama-3.2-90b-text-preview",
    deepseek: "deepseek-chat",
    github: "gpt-4o",
    ollama: "llama3.2",
  };

  return fallbacks[modelName] || "gpt-4o";
}

/**
 * Get all variants for a model
 */
export function getVariants(modelName: string): ModelVariant[] {
  return ModelVariants[modelName as keyof typeof ModelVariants] || [];
}

/**
 * Get variants for a model including custom Ollama variant if configured
 */
export function getVariantsWithCustom(
  modelName: string,
  customOllamaVariant?: string
): ModelVariant[] {
  const baseVariants = getVariants(modelName);

  // For Ollama, add custom variant if specified
  if (
    modelName === "ollama" &&
    customOllamaVariant &&
    customOllamaVariant.trim()
  ) {
    const customVariant: ModelVariant = {
      name: customOllamaVariant.trim(),
      displayName: `${customOllamaVariant.trim()} (Custom)`,
      description: "Custom Ollama model",
    };

    // Check if custom variant already exists in predefined variants
    const existsInPredefined = baseVariants.some(
      (v) => v.name === customVariant.name
    );
    if (!existsInPredefined) {
      return [customVariant, ...baseVariants];
    }
  }

  return baseVariants;
}

/**
 * Get all supported models
 */
export function getSupportedModels(): string[] {
  return Object.keys(ModelVariants);
}

/**
 * Check if a model is supported
 */
export function isModelSupported(modelName: string): boolean {
  return getSupportedModels().includes(modelName);
}

/**
 * Get model display name
 */
export function getModelDisplayName(modelName: string): string {
  const displayNames: { [key: string]: string } = {
    openai: "OpenAI",
    claude: "Anthropic Claude",
    gemini: "Google Gemini",
    grok: "xAI Grok",
    llama: "Meta Llama",
    deepseek: "DeepSeek",
    github: "GitHub Models",
    ollama: "Ollama",
  };

  return displayNames[modelName] || modelName;
}

/**
 * Find variant by name in a model
 */
export function findVariant(
  modelName: string,
  variantName: string
): ModelVariant | undefined {
  const variants = getVariants(modelName);
  return variants.find((v) => v.name === variantName);
}

/**
 * Get variant display name
 */
export function getVariantDisplayName(
  modelName: string,
  variantName: string
): string {
  const variant = findVariant(modelName, variantName);
  return variant ? variant.displayName : variantName;
}
