import axios, { AxiosResponse } from 'axios';
import { CommitGenerator } from './commitGenerator';
import { Language } from './language';
import { getCommitPrompt, getAnalysisPrompt } from '../utils/commitUtils';
import { getDefaultVariant } from './modelVariants';
import { <PERSON>rrorHandler } from '../utils/errorHandler';

/**
 * Meta Llama commit message generator
 * Based on the LlamaGenerator from the reference Dart implementation
 * 
 * Note: This uses a generic Llama API endpoint. In practice, you might need to
 * adjust the endpoint based on your specific Llama API provider (e.g., Together AI,
 * Replicate, or other hosted Llama services).
 */
export class LlamaGenerator extends CommitGenerator {
    constructor(apiKey: string, variant?: string) {
        super(apiKey, variant);
    }

    get modelName(): string {
        return 'llama';
    }

    get defaultVariant(): string {
        return getDefaultVariant(this.modelName);
    }

    async generateCommitMessage(
        diff: string,
        language: Language,
        prefix?: string
    ): Promise<string> {
        const prompt = getCommitPrompt(diff, language, prefix);

        try {
            // Note: This endpoint is from the reference implementation
            // You may need to adjust based on your Llama API provider
            const response: AxiosResponse = await axios.post(
                'https://api.llama.api/v1/completions',
                {
                    model: this.actualVariant,
                    prompt: prompt,
                    max_tokens: this.maxTokens
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    }
                }
            );

            if (response.status === 200) {
                return response.data.choices[0].text.trim();
            } else {
                throw new Error(`Unexpected response from Llama API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'llama');
        }
    }

    async analyzeChanges(diff: string, language: Language): Promise<string> {
        const prompt = getAnalysisPrompt(diff, language);

        try {
            const response: AxiosResponse = await axios.post(
                'https://api.llama.api/v1/completions',
                {
                    model: this.actualVariant,
                    prompt: prompt,
                    max_tokens: this.maxAnalysisTokens
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    }
                }
            );

            if (response.status === 200) {
                return response.data.choices[0].text.trim();
            } else {
                throw new Error(`Unexpected response from Llama API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'llama');
        }
    }
}
