/**
 * Language support for GitWhisper commit messages
 * Based on the Language enum from the reference Dart implementation
 */

export interface Language {
    code: string;
    countryCode: string;
    name: string;
    displayName: string;
}

export const Languages = {
    english: {
        code: 'en',
        countryCode: 'US',
        name: 'english',
        displayName: 'English'
    } as Language,
    
    spanish: {
        code: 'es',
        countryCode: 'ES',
        name: 'spanish',
        displayName: 'Español'
    } as Language,
    
    french: {
        code: 'fr',
        countryCode: 'FR',
        name: 'french',
        displayName: 'Français'
    } as Language,
    
    german: {
        code: 'de',
        countryCode: 'DE',
        name: 'german',
        displayName: 'Deutsch'
    } as Language,
    
    italian: {
        code: 'it',
        countryCode: 'IT',
        name: 'italian',
        displayName: 'Italiano'
    } as Language,
    
    portuguese: {
        code: 'pt',
        countryCode: 'BR',
        name: 'portuguese',
        displayName: 'Português'
    } as Language,
    
    russian: {
        code: 'ru',
        countryCode: 'RU',
        name: 'russian',
        displayName: 'Русский'
    } as Language,
    
    chinese: {
        code: 'zh',
        countryCode: 'CN',
        name: 'chinese',
        displayName: '中文'
    } as Language,
    
    japanese: {
        code: 'ja',
        countryCode: 'JP',
        name: 'japanese',
        displayName: '日本語'
    } as Language,
    
    korean: {
        code: 'ko',
        countryCode: 'KR',
        name: 'korean',
        displayName: '한국어'
    } as Language,
    
    arabic: {
        code: 'ar',
        countryCode: 'SA',
        name: 'arabic',
        displayName: 'العربية'
    } as Language,
    
    hindi: {
        code: 'hi',
        countryCode: 'IN',
        name: 'hindi',
        displayName: 'हिन्दी'
    } as Language,
    
    dutch: {
        code: 'nl',
        countryCode: 'NL',
        name: 'dutch',
        displayName: 'Nederlands'
    } as Language,
    
    swedish: {
        code: 'sv',
        countryCode: 'SE',
        name: 'swedish',
        displayName: 'Svenska'
    } as Language,
    
    norwegian: {
        code: 'no',
        countryCode: 'NO',
        name: 'norwegian',
        displayName: 'Norsk'
    } as Language,
    
    danish: {
        code: 'da',
        countryCode: 'DK',
        name: 'danish',
        displayName: 'Dansk'
    } as Language,
    
    finnish: {
        code: 'fi',
        countryCode: 'FI',
        name: 'finnish',
        displayName: 'Suomi'
    } as Language,
    
    polish: {
        code: 'pl',
        countryCode: 'PL',
        name: 'polish',
        displayName: 'Polski'
    } as Language,
    
    czech: {
        code: 'cs',
        countryCode: 'CZ',
        name: 'czech',
        displayName: 'Čeština'
    } as Language,
    
    hungarian: {
        code: 'hu',
        countryCode: 'HU',
        name: 'hungarian',
        displayName: 'Magyar'
    } as Language,
    
    romanian: {
        code: 'ro',
        countryCode: 'RO',
        name: 'romanian',
        displayName: 'Română'
    } as Language,
    
    bulgarian: {
        code: 'bg',
        countryCode: 'BG',
        name: 'bulgarian',
        displayName: 'Български'
    } as Language,
    
    greek: {
        code: 'el',
        countryCode: 'GR',
        name: 'greek',
        displayName: 'Ελληνικά'
    } as Language,
    
    turkish: {
        code: 'tr',
        countryCode: 'TR',
        name: 'turkish',
        displayName: 'Türkçe'
    } as Language,
    
    hebrew: {
        code: 'he',
        countryCode: 'IL',
        name: 'hebrew',
        displayName: 'עברית'
    } as Language,
    
    thai: {
        code: 'th',
        countryCode: 'TH',
        name: 'thai',
        displayName: 'ไทย'
    } as Language,
    
    vietnamese: {
        code: 'vi',
        countryCode: 'VN',
        name: 'vietnamese',
        displayName: 'Tiếng Việt'
    } as Language,
    
    indonesian: {
        code: 'id',
        countryCode: 'ID',
        name: 'indonesian',
        displayName: 'Bahasa Indonesia'
    } as Language,
    
    malay: {
        code: 'ms',
        countryCode: 'MY',
        name: 'malay',
        displayName: 'Bahasa Melayu'
    } as Language,
    
    ukrainian: {
        code: 'uk',
        countryCode: 'UA',
        name: 'ukrainian',
        displayName: 'Українська'
    } as Language,
    
    croatian: {
        code: 'hr',
        countryCode: 'HR',
        name: 'croatian',
        displayName: 'Hrvatski'
    } as Language,
    
    serbian: {
        code: 'sr',
        countryCode: 'RS',
        name: 'serbian',
        displayName: 'Српски'
    } as Language,
    
    slovak: {
        code: 'sk',
        countryCode: 'SK',
        name: 'slovak',
        displayName: 'Slovenčina'
    } as Language,
    
    slovenian: {
        code: 'sl',
        countryCode: 'SI',
        name: 'slovenian',
        displayName: 'Slovenščina'
    } as Language,
    
    lithuanian: {
        code: 'lt',
        countryCode: 'LT',
        name: 'lithuanian',
        displayName: 'Lietuvių'
    } as Language,
    
    latvian: {
        code: 'lv',
        countryCode: 'LV',
        name: 'latvian',
        displayName: 'Latviešu'
    } as Language,
    
    estonian: {
        code: 'et',
        countryCode: 'EE',
        name: 'estonian',
        displayName: 'Eesti'
    } as Language
};

/**
 * Get all available languages as an array
 */
export function getAllLanguages(): Language[] {
    return Object.values(Languages);
}

/**
 * Find a language by its code and country code
 */
export function findLanguage(code: string, countryCode: string): Language | undefined {
    return getAllLanguages().find(lang => 
        lang.code === code && lang.countryCode === countryCode
    );
}

/**
 * Parse language string in format "code;countryCode"
 */
export function parseLanguageString(languageString: string): Language {
    const parts = languageString.split(';');
    if (parts.length === 2) {
        const found = findLanguage(parts[0], parts[1]);
        if (found) {
            return found;
        }
    }
    return Languages.english; // Default fallback
}

/**
 * Convert language to string format "code;countryCode"
 */
export function languageToString(language: Language): string {
    return `${language.code};${language.countryCode}`;
}
