import axios, { AxiosResponse } from 'axios';
import { CommitGenerator } from './commitGenerator';
import { Language } from './language';
import { getCommitPrompt, getAnalysisPrompt } from '../utils/commitUtils';
import { getDefaultVariant } from './modelVariants';

/**
 * Anthropic Claude commit message generator
 * Based on the ClaudeGenerator from the reference Dart implementation
 */
export class ClaudeGenerator extends CommitGenerator {
    private readonly baseUrl = 'https://api.anthropic.com/v1';

    constructor(apiKey: string | undefined, variant?: string) {
        super(apiKey, variant);
    }

    get modelName(): string {
        return 'claude';
    }

    get defaultVariant(): string {
        return getDefaultVariant('claude');
    }

    async generateCommitMessage(
        diff: string,
        language: Language,
        prefix?: string
    ): Promise<string> {
        this.validateConfiguration();
        
        const prompt = getCommitPrompt(diff, language, prefix);

        try {
            const response: AxiosResponse = await axios.post(
                `${this.baseUrl}/messages`,
                {
                    model: this.actualVariant,
                    max_tokens: this.maxTokens,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    temperature: 0.1,
                    top_p: 0.9
                },
                {
                    headers: {
                        ...this.getCommonHeaders(),
                        'x-api-key': this.apiKey,
                        'anthropic-version': '2023-06-01'
                    },
                    timeout: this.getTimeoutConfig()
                }
            );

            if (response.status === 200 && response.data?.content?.[0]?.text) {
                const message = response.data.content[0].text.trim();
                const cleanedMessage = this.cleanResponse(message);
                this.validateCommitMessage(cleanedMessage);
                return cleanedMessage;
            } else {
                throw new Error(`Invalid response format from Claude API`);
            }
        } catch (error: any) {
            this.handleApiError(error, 'generating commit message');
        }
    }

    async analyzeChanges(diff: string, language: Language): Promise<string> {
        this.validateConfiguration();
        
        const prompt = getAnalysisPrompt(diff, language);

        try {
            const response: AxiosResponse = await axios.post(
                `${this.baseUrl}/messages`,
                {
                    model: this.actualVariant,
                    max_tokens: this.maxAnalysisTokens,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    temperature: 0.2,
                    top_p: 0.9
                },
                {
                    headers: {
                        ...this.getCommonHeaders(),
                        'x-api-key': this.apiKey,
                        'anthropic-version': '2023-06-01'
                    },
                    timeout: this.getTimeoutConfig()
                }
            );

            if (response.status === 200 && response.data?.content?.[0]?.text) {
                return response.data.content[0].text.trim();
            } else {
                throw new Error(`Invalid response format from Claude API`);
            }
        } catch (error: any) {
            this.handleApiError(error, 'analyzing changes');
        }
    }
}
