import axios, { AxiosResponse } from "axios";
import { CommitGenerator } from "./commitGenerator";
import { Language } from "./language";
import { getCommitPrompt, getAnalysisPrompt } from "../utils/commitUtils";
import { getDefaultVariant } from "./modelVariants";
import { ErrorHandler } from "../utils/errorHandler";

/**
 * Ollama local AI commit message generator
 * Based on the OllamaGenerator from the reference Dart implementation
 *
 * Ollama runs AI models locally, so no API key is required
 */
export class OllamaGenerator extends CommitGenerator {
  private baseUrl: string;

  constructor(
    apiKey: string = "",
    variant?: string,
    baseUrl: string = "http://localhost:11434"
  ) {
    super(apiKey, variant); // API key not used for Ollama
    this.baseUrl = baseUrl;
  }

  get modelName(): string {
    return "ollama";
  }

  get defaultVariant(): string {
    return getDefaultVariant(this.modelName);
  }

  async generateCommitMessage(
    diff: string,
    language: Language,
    prefix?: string
  ): Promise<string> {
    const prompt = getCommitPrompt(diff, language, prefix);

    console.log(`[Ollama] Starting commit generation...`);
    console.log(`[Ollama] Using model: ${this.actualVariant}`);
    console.log(`[Ollama] Base URL: ${this.baseUrl}`);
    console.log(`[Ollama] Prompt length: ${prompt.length}`);
    console.log(`[Ollama] Diff preview: ${diff.substring(0, 200)}...`);

    try {
      const requestData = {
        model: this.actualVariant,
        prompt: prompt,
        stream: false,
        options: {
          num_predict: this.maxTokens,
        },
      };

      console.log(
        `[Ollama] Request data:`,
        JSON.stringify(requestData, null, 2)
      );

      const response: AxiosResponse = await axios.post(
        `${this.baseUrl}/api/generate`,
        requestData,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 60000, // 60 second timeout
        }
      );

      console.log(`[Ollama] Response status: ${response.status}`);
      console.log(
        `[Ollama] Response data:`,
        JSON.stringify(response.data, null, 2)
      );

      if (response.status === 200) {
        if (response.data && response.data.response) {
          let cleanedResponse = response.data.response.trim();

          // Clean up DeepSeek R1 and other models' thinking process
          cleanedResponse = this.cleanOllamaResponse(cleanedResponse);

          return cleanedResponse;
        } else {
          throw new Error(
            `Invalid response format from Ollama API: ${JSON.stringify(
              response.data
            )}`
          );
        }
      } else {
        throw new Error(
          `Unexpected response from Ollama API: ${response.status}`
        );
      }
    } catch (error: any) {
      console.error(`[Ollama] Error:`, error);
      throw ErrorHandler.parseHttpError(error, "ollama");
    }
  }

  /**
   * Clean Ollama response by removing thinking process and formatting properly
   */
  private cleanOllamaResponse(response: string): string {
    let cleaned = response;

    // Remove DeepSeek R1's thinking tags and content
    cleaned = cleaned.replace(/<think>[\s\S]*?<\/think>/gi, "");

    // Remove any remaining XML-style tags
    cleaned = cleaned.replace(/<[^>]*>/g, "");

    // Remove markdown code blocks
    cleaned = cleaned.replace(/```[\s\S]*?```/g, "");

    // Remove excessive whitespace and newlines
    cleaned = cleaned.replace(/\n\s*\n\s*\n/g, "\n\n");
    cleaned = cleaned.trim();

    // If response starts with multiple formats, try to extract just the commit message
    const lines = cleaned.split("\n");
    const commitLineIndex = lines.findIndex(
      (line) =>
        line.match(
          /^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert):/i
        ) || line.match(/^[a-zA-Z]+:\s*[🎨🐛📚💄♻️🧪🔧⚡👷📦⏪]/i)
    );

    if (commitLineIndex !== -1) {
      // Found a conventional commit format, use just that line
      let commitMessage = lines[commitLineIndex].trim();

      // Validate and truncate if too long
      if (commitMessage.length > 72) {
        console.log(
          `[Ollama] Commit message too long (${commitMessage.length} chars), truncating...`
        );
        commitMessage = this.truncateCommitMessage(commitMessage);
      }

      return commitMessage;
    }

    // If no conventional commit found, take the first non-empty meaningful line
    const meaningfulLine = lines.find(
      (line) =>
        line.trim().length > 10 &&
        !line.includes("**") &&
        !line.toLowerCase().includes("adding") &&
        !line.toLowerCase().includes("description")
    );

    if (meaningfulLine) {
      let commitMessage = meaningfulLine.trim();
      if (commitMessage.length > 72) {
        commitMessage = this.truncateCommitMessage(commitMessage);
      }
      return commitMessage;
    }

    // Fallback: return first substantial line
    const firstLine = lines.find((line) => line.trim().length > 5);
    let result = firstLine ? firstLine.trim() : cleaned;

    if (result.length > 72) {
      result = this.truncateCommitMessage(result);
    }

    return result;
  }

  /**
   * Truncate commit message to fit within 72 characters while preserving meaning
   */
  private truncateCommitMessage(message: string): string {
    if (message.length <= 72) {
      return message;
    }

    // Try to find a good break point
    const parts = message.match(/^([^:]+:\s*[^\s]+\s*)(.*)$/);
    if (parts) {
      const typeAndEmoji = parts[1]; // "feat: ✨ "
      const description = parts[2];

      const availableLength = 72 - typeAndEmoji.length;
      if (availableLength > 10) {
        // Truncate description and add ellipsis if needed
        let truncatedDesc = description.substring(0, availableLength - 3);

        // Try to break at word boundary
        const lastSpace = truncatedDesc.lastIndexOf(" ");
        if (lastSpace > availableLength / 2) {
          truncatedDesc = truncatedDesc.substring(0, lastSpace);
        }

        return typeAndEmoji + truncatedDesc + "...";
      }
    }

    // Fallback: simple truncation
    return message.substring(0, 69) + "...";
  }

  /**
   * Clean analysis response - less aggressive cleaning for detailed analysis
   */
  private cleanAnalysisResponse(response: string): string {
    let cleaned = response;

    // Remove DeepSeek R1's thinking tags and content
    cleaned = cleaned.replace(/<think>[\s\S]*?<\/think>/gi, "");

    // Remove any remaining XML-style tags
    cleaned = cleaned.replace(/<[^>]*>/g, "");

    // Remove excessive whitespace and newlines but preserve structure
    cleaned = cleaned.replace(/\n\s*\n\s*\n\s*\n/g, "\n\n");
    cleaned = cleaned.trim();

    return cleaned;
  }

  async analyzeChanges(diff: string, language: Language): Promise<string> {
    const prompt = getAnalysisPrompt(diff, language);

    console.log(`[Ollama] Analyzing changes with model: ${this.actualVariant}`);

    try {
      const requestData = {
        model: this.actualVariant,
        prompt: prompt,
        stream: false,
        options: {
          num_predict: this.maxAnalysisTokens,
        },
      };

      const response: AxiosResponse = await axios.post(
        `${this.baseUrl}/api/generate`,
        requestData,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 60000, // 60 second timeout
        }
      );

      console.log(`[Ollama] Analysis response status: ${response.status}`);

      if (response.status === 200) {
        if (response.data && response.data.response) {
          let cleanedResponse = response.data.response.trim();

          // For analysis, we want more content but still clean up thinking process
          cleanedResponse = this.cleanAnalysisResponse(cleanedResponse);

          return cleanedResponse;
        } else {
          throw new Error(
            `Invalid response format from Ollama API: ${JSON.stringify(
              response.data
            )}`
          );
        }
      } else {
        throw new Error(
          `Unexpected response from Ollama API: ${response.status}`
        );
      }
    } catch (error: any) {
      console.error(`[Ollama] Analysis error:`, error);
      throw ErrorHandler.parseHttpError(error, "ollama");
    }
  }
}
