import axios, { AxiosResponse } from 'axios';
import { CommitGenerator } from './commitGenerator';
import { Language } from './language';
import { getCommitPrompt, getAnalysisPrompt } from '../utils/commitUtils';
import { getDefaultVariant } from './modelVariants';

/**
 * OpenAI GPT commit message generator
 * Based on the OpenAIGenerator from the reference Dart implementation
 */
export class OpenAIGenerator extends CommitGenerator {
    private readonly baseUrl = 'https://api.openai.com/v1';

    constructor(apiKey: string | undefined, variant?: string) {
        super(apiKey, variant);
    }

    get modelName(): string {
        return 'openai';
    }

    get defaultVariant(): string {
        return getDefaultVariant('openai');
    }

    async generateCommitMessage(
        diff: string,
        language: Language,
        prefix?: string
    ): Promise<string> {
        this.validateConfiguration();
        
        const prompt = getCommitPrompt(diff, language, prefix);

        try {
            const response: AxiosResponse = await axios.post(
                `${this.baseUrl}/chat/completions`,
                {
                    model: this.actualVariant,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    max_tokens: this.maxTokens,
                    temperature: 0.1,
                    top_p: 0.9
                },
                {
                    headers: {
                        ...this.getCommonHeaders(),
                        'Authorization': `Bearer ${this.apiKey}`
                    },
                    timeout: this.getTimeoutConfig()
                }
            );

            if (response.status === 200 && response.data?.choices?.[0]?.message?.content) {
                const message = response.data.choices[0].message.content.trim();
                const cleanedMessage = this.cleanResponse(message);
                this.validateCommitMessage(cleanedMessage);
                return cleanedMessage;
            } else {
                throw new Error(`Invalid response format from OpenAI API`);
            }
        } catch (error: any) {
            this.handleApiError(error, 'generating commit message');
        }
    }

    async analyzeChanges(diff: string, language: Language): Promise<string> {
        this.validateConfiguration();
        
        const prompt = getAnalysisPrompt(diff, language);

        try {
            const response: AxiosResponse = await axios.post(
                `${this.baseUrl}/chat/completions`,
                {
                    model: this.actualVariant,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    max_tokens: this.maxAnalysisTokens,
                    temperature: 0.2,
                    top_p: 0.9
                },
                {
                    headers: {
                        ...this.getCommonHeaders(),
                        'Authorization': `Bearer ${this.apiKey}`
                    },
                    timeout: this.getTimeoutConfig()
                }
            );

            if (response.status === 200 && response.data?.choices?.[0]?.message?.content) {
                return response.data.choices[0].message.content.trim();
            } else {
                throw new Error(`Invalid response format from OpenAI API`);
            }
        } catch (error: any) {
            this.handleApiError(error, 'analyzing changes');
        }
    }
}
