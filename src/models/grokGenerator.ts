import axios, { AxiosResponse } from 'axios';
import { CommitGenerator } from './commitGenerator';
import { Language } from './language';
import { getCommitPrompt, getAnalysisPrompt } from '../utils/commitUtils';
import { getDefaultVariant } from './modelVariants';
import { ErrorHandler } from '../utils/errorHandler';

/**
 * xAI Grok commit message generator
 * Based on the GrokGenerator from the reference Dart implementation
 */
export class GrokGenerator extends CommitGenerator {
    constructor(apiKey: string, variant?: string) {
        super(apiKey, variant);
    }

    get modelName(): string {
        return 'grok';
    }

    get defaultVariant(): string {
        return getDefaultVariant(this.modelName);
    }

    async generateCommitMessage(
        diff: string,
        language: Language,
        prefix?: string
    ): Promise<string> {
        const prompt = getCommitPrompt(diff, language, prefix);

        try {
            const response: AxiosResponse = await axios.post(
                'https://api.x.ai/v1/chat/completions',
                {
                    model: this.actualVariant,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    max_tokens: this.maxTokens
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    }
                }
            );

            if (response.status === 200) {
                return response.data.choices[0].message.content.trim();
            } else {
                throw new Error(`Unexpected response from Grok API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'grok');
        }
    }

    async analyzeChanges(diff: string, language: Language): Promise<string> {
        const prompt = getAnalysisPrompt(diff, language);

        try {
            const response: AxiosResponse = await axios.post(
                'https://api.x.ai/v1/chat/completions',
                {
                    model: this.actualVariant,
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    max_tokens: this.maxAnalysisTokens
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    }
                }
            );

            if (response.status === 200) {
                return response.data.choices[0].message.content.trim();
            } else {
                throw new Error(`Unexpected response from Grok API: ${response.status}`);
            }
        } catch (error: any) {
            throw ErrorHandler.parseHttpError(error, 'grok');
        }
    }
}
