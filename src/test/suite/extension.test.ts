import * as assert from "assert";
import * as vscode from "vscode";
import { ConfigManager } from "../../config/configManager";
import { ValidationUtils } from "../../utils/validation";
import { ErrorHand<PERSON>, ApiException } from "../../utils/errorHandler";
import { GitUtils } from "../../utils/gitUtils";
import { CommitGeneratorFactory } from "../../models/commitGeneratorFactory";

suite("GitWhisper Extension Test Suite", () => {
  vscode.window.showInformationMessage("Start all tests.");

  suite("ConfigManager Tests", () => {
    let configManager: ConfigManager;

    setup(() => {
      configManager = new ConfigManager();
    });

    test("Should get default language", () => {
      const language = configManager.getWhisperLanguage();
      assert.ok(language);
      assert.strictEqual(typeof language.code, "string");
      assert.strictEqual(typeof language.displayName, "string");
    });

    test("Should handle missing configuration gracefully", () => {
      // Test that config manager doesn't throw on missing values
      const alwaysAdd = configManager.shouldAlwaysAdd();
      const autoPush = configManager.shouldAutoPush();

      assert.strictEqual(typeof alwaysAdd, "boolean");
      assert.strictEqual(typeof autoPush, "boolean");
    });
  });

  suite("ValidationUtils Tests", () => {
    test("Should validate API keys correctly", () => {
      // Test OpenAI API key validation
      const validOpenAI = ValidationUtils.validateApiKey(
        "sk-1234567890abcdef1234567890abcdef1234567890abcdef12",
        "openai"
      );
      assert.strictEqual(validOpenAI.isValid, true);

      const invalidOpenAI = ValidationUtils.validateApiKey(
        "invalid-key",
        "openai"
      );
      assert.strictEqual(invalidOpenAI.isValid, false);
      assert.ok(invalidOpenAI.error);
    });

    test("Should validate commit messages", () => {
      const validMessage = ValidationUtils.validateCommitMessage(
        "feat: add new feature"
      );
      assert.strictEqual(validMessage.isValid, true);

      const emptyMessage = ValidationUtils.validateCommitMessage("");
      assert.strictEqual(emptyMessage.isValid, false);
      assert.ok(emptyMessage.error);

      const tooLongMessage = ValidationUtils.validateCommitMessage(
        "a".repeat(101)
      );
      assert.strictEqual(tooLongMessage.isValid, false);
      assert.ok(tooLongMessage.error);
    });

    test("Should validate language codes", () => {
      const validLanguage = ValidationUtils.validateLanguage("en");
      assert.strictEqual(validLanguage.isValid, true);

      const invalidLanguage = ValidationUtils.validateLanguage("invalid-lang");
      assert.strictEqual(invalidLanguage.isValid, false);
      assert.ok(invalidLanguage.error);
    });

    test("Should validate model and variant combinations", () => {
      const validCombination = ValidationUtils.validateModelVariant(
        "openai",
        "gpt-3.5-turbo"
      );
      assert.strictEqual(validCombination.isValid, true);

      const invalidModel = ValidationUtils.validateModelVariant(
        "invalid-model",
        "gpt-4"
      );
      assert.strictEqual(invalidModel.isValid, false);
      assert.ok(invalidModel.error);
    });
  });

  suite("ErrorHandler Tests", () => {
    test("Should create appropriate exception types", () => {
      const authError = new ApiException(
        "Unauthorized",
        401,
        "authentication_error"
      );
      assert.strictEqual(authError.statusCode, 401);
      assert.strictEqual(authError.errorType, "authentication_error");
      assert.ok(authError.message.includes("Unauthorized"));
    });

    test("Should parse HTTP errors correctly", () => {
      const mockError = {
        response: {
          status: 429,
          headers: {
            "retry-after": "60",
          },
          data: {
            error: {
              message: "Rate limit exceeded",
            },
          },
        },
      };

      const parsedError = ErrorHandler.parseHttpError(mockError, "openai");
      assert.ok(parsedError instanceof ApiException);
      assert.strictEqual(parsedError.statusCode, 429);
    });

    test("Should suggest model switching for certain errors", () => {
      const rateLimitError = new ApiException(
        "Rate limit exceeded",
        429,
        "rate_limit_error"
      );
      const shouldSwitch =
        ErrorHandler.shouldSuggestModelSwitch(rateLimitError);
      assert.strictEqual(shouldSwitch, true);

      const authError = new ApiException(
        "Unauthorized",
        401,
        "authentication_error"
      );
      const shouldSwitchAuth = ErrorHandler.shouldSuggestModelSwitch(authError);
      assert.strictEqual(shouldSwitchAuth, true);
    });
  });

  suite("CommitGeneratorFactory Tests", () => {
    test("Should return available models", () => {
      const models = CommitGeneratorFactory.getAvailableModels();
      assert.ok(Array.isArray(models));
      assert.ok(models.length > 0);
      assert.ok(models.includes("openai"));
      assert.ok(models.includes("claude"));
    });

    test("Should create generators for valid models", () => {
      const generator = CommitGeneratorFactory.create("openai", "test-key");
      assert.ok(generator);
      assert.strictEqual(typeof generator.generateCommitMessage, "function");
      assert.strictEqual(typeof generator.analyzeChanges, "function");
    });

    test("Should throw error for invalid models", () => {
      assert.throws(() => {
        CommitGeneratorFactory.create("invalid-model", "test-key");
      });
    });
  });

  suite("GitUtils Tests", () => {
    test("Should strip markdown code blocks", () => {
      const input = "```\nfeat: add new feature\n```";
      const output = GitUtils.stripMarkdownCodeBlocks(input);
      assert.strictEqual(output, "feat: add new feature");
    });

    test("Should handle text without code blocks", () => {
      const input = "feat: add new feature";
      const output = GitUtils.stripMarkdownCodeBlocks(input);
      assert.strictEqual(output, "feat: add new feature");
    });

    test("Should handle multiple code blocks", () => {
      const input =
        "```\nfeat: add feature\n```\n\nSome text\n\n```\nfix: bug\n```";
      const output = GitUtils.stripMarkdownCodeBlocks(input);
      assert.strictEqual(output, "feat: add feature\n\nSome text\n\nfix: bug");
    });
  });

  suite("Integration Tests", () => {
    test("Should handle complete workflow without errors", async () => {
      // This is a basic integration test that ensures all components work together
      const configManager = new ConfigManager();

      // Test configuration retrieval
      const language = configManager.getWhisperLanguage();
      assert.ok(language);

      // Test validation
      const validation = ValidationUtils.validateLanguage(language.code);
      assert.strictEqual(validation.isValid, true);

      // Test model availability
      const models = CommitGeneratorFactory.getAvailableModels();
      assert.ok(models.length > 0);
    });
  });
});
