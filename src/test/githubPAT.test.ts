import * as assert from "assert";
import * as vscode from "vscode";
import { ConfigManager } from "../config/configManager";
import { GitUtils } from "../utils/gitUtils";

suite("GitHub PAT Tests", () => {
  let configManager: ConfigManager;
  let gitUtils: GitUtils;

  setup(() => {
    configManager = new ConfigManager();
    // Use current working directory for tests
    gitUtils = new GitUtils(process.cwd());
  });

  test("Should validate GitHub PAT format", async () => {
    // Test empty PAT
    try {
      await configManager.setGitHubPAT("");
      assert.fail("Should have rejected empty PAT");
    } catch (error) {
      // Expected to fail
    }

    // Test short PAT
    try {
      await configManager.setGitHubPAT("short");
      assert.fail("Should have rejected short PAT");
    } catch (error) {
      // Expected to fail
    }
  });

  test("Should detect GitHub repositories", async () => {
    // This test would need a real git repository to work properly
    // For now, we just test that the method exists and doesn't throw
    const isGitHub = await gitUtils.isGitHubRepository();
    assert.strictEqual(typeof isGitHub, "boolean");
  });

  test("Should handle GitHub URL transformations", () => {
    // Test URL transformation logic (this would be in a helper function)
    const httpsUrl = "https://github.com/user/repo.git";
    const sshUrl = "**************:user/repo.git";
    const token = "ghp_test_token";

    // HTTPS URL should get token injected
    const expectedHttps = `https://${token}@github.com/user/repo.git`;
    assert.strictEqual(
      httpsUrl.replace("https://github.com/", `https://${token}@github.com/`),
      expectedHttps
    );

    // SSH URL should be converted to HTTPS with token
    const repoPath = sshUrl.replace("**************:", "").replace(".git", "");
    const expectedFromSsh = `https://${token}@github.com/${repoPath}.git`;
    assert.strictEqual(
      expectedFromSsh,
      `https://${token}@github.com/user/repo.git`
    );
  });

  test("Should have GitHub PAT methods in ConfigManager", () => {
    assert.strictEqual(typeof configManager.getGitHubPAT, "function");
    assert.strictEqual(typeof configManager.setGitHubPAT, "function");
  });

  test("Should have commit and push methods in GitUtils", () => {
    assert.strictEqual(typeof gitUtils.runGitCommitAndPush, "function");
    assert.strictEqual(typeof gitUtils.isGitHubRepository, "function");
  });
});
