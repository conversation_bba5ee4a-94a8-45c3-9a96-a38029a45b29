import * as path from "path";

/**
 * Utility class for filtering files based on ignore patterns
 */
export class FileFilter {
  /**
   * Check if a file path matches any of the ignore patterns
   * Supports glob patterns like *.log, node_modules/*, etc.
   */
  static shouldIgnoreFile(filePath: string, ignorePatterns: string[]): boolean {
    const normalizedPath = path.normalize(filePath);
    const fileName = path.basename(normalizedPath);
    
    for (const pattern of ignorePatterns) {
      if (this.matchesPattern(normalizedPath, pattern) || 
          this.matchesPattern(fileName, pattern)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Filter a list of file paths, removing those that match ignore patterns
   */
  static filterFiles(filePaths: string[], ignorePatterns: string[]): string[] {
    return filePaths.filter(filePath => !this.shouldIgnoreFile(filePath, ignorePatterns));
  }

  /**
   * Filter diff content by removing ignored files
   */
  static filterDiff(diff: string, ignorePatterns: string[]): string {
    if (!diff || ignorePatterns.length === 0) {
      return diff;
    }

    // Split diff into file sections
    const filePattern = /^diff --git a\/(.+?) b\/(.+?)$/gm;
    const sections: Array<{ filename: string; content: string; startIndex: number; endIndex: number }> = [];
    
    let match;
    let lastIndex = 0;
    
    while ((match = filePattern.exec(diff)) !== null) {
      // Add previous section if exists
      if (lastIndex > 0 && sections.length > 0) {
        sections[sections.length - 1].endIndex = match.index;
      }
      
      // Start new section
      const filename = match[1];
      sections.push({
        filename,
        content: '',
        startIndex: match.index,
        endIndex: diff.length
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    // Update the last section's end index
    if (sections.length > 0) {
      sections[sections.length - 1].endIndex = diff.length;
    }
    
    // Extract content for each section
    sections.forEach(section => {
      section.content = diff.substring(section.startIndex, section.endIndex);
    });
    
    // Filter out ignored files
    const filteredSections = sections.filter(section => 
      !this.shouldIgnoreFile(section.filename, ignorePatterns)
    );
    
    // Reconstruct diff
    return filteredSections.map(section => section.content).join('');
  }

  /**
   * Check if a path matches a glob pattern
   * Supports basic glob patterns: *, ?, **
   */
  private static matchesPattern(filePath: string, pattern: string): boolean {
    // Exact match
    if (filePath === pattern) {
      return true;
    }
    
    // Convert glob pattern to regex
    const regexPattern = this.globToRegex(pattern);
    const regex = new RegExp(regexPattern, 'i'); // Case insensitive
    
    return regex.test(filePath);
  }

  /**
   * Convert a glob pattern to a regular expression
   */
  private static globToRegex(pattern: string): string {
    // Escape special regex characters except glob characters
    let regexPattern = pattern
      .replace(/[.+^${}()|[\]\\]/g, '\\$&') // Escape regex special chars
      .replace(/\*/g, '.*') // * matches any characters
      .replace(/\?/g, '.') // ? matches single character
      .replace(/\\\*\\\*/g, '.*'); // ** matches any path
    
    // Handle directory separators
    regexPattern = regexPattern.replace(/\//g, '[/\\\\]'); // Match both / and \
    
    // Anchor the pattern
    return `^${regexPattern}$`;
  }

  /**
   * Get a summary of filtered files
   */
  static getFilterSummary(originalFiles: string[], filteredFiles: string[], ignorePatterns: string[]): string {
    const ignoredCount = originalFiles.length - filteredFiles.length;
    
    if (ignoredCount === 0) {
      return "No files were ignored";
    }
    
    const ignoredFiles = originalFiles.filter(file => !filteredFiles.includes(file));
    
    return `Ignored ${ignoredCount} file(s): ${ignoredFiles.slice(0, 3).join(', ')}${
      ignoredFiles.length > 3 ? ` and ${ignoredFiles.length - 3} more` : ''
    }`;
  }

  /**
   * Validate ignore patterns
   */
  static validateIgnorePatterns(patterns: string[]): { valid: string[]; invalid: string[] } {
    const valid: string[] = [];
    const invalid: string[] = [];
    
    for (const pattern of patterns) {
      try {
        // Test if the pattern can be converted to a valid regex
        this.globToRegex(pattern);
        valid.push(pattern);
      } catch (error) {
        invalid.push(pattern);
      }
    }
    
    return { valid, invalid };
  }

  /**
   * Get common ignore patterns for different project types
   */
  static getCommonIgnorePatterns(projectType?: string): string[] {
    const common = [
      "*.log",
      "*.tmp",
      "*.temp",
      ".DS_Store",
      "Thumbs.db"
    ];
    
    switch (projectType?.toLowerCase()) {
      case 'node':
      case 'javascript':
      case 'typescript':
        return [
          ...common,
          "package-lock.json",
          "yarn.lock",
          "pnpm-lock.yaml",
          "node_modules/*",
          "dist/*",
          "build/*",
          "*.min.js",
          "*.min.css"
        ];
      
      case 'python':
        return [
          ...common,
          "__pycache__/*",
          "*.pyc",
          "*.pyo",
          "*.pyd",
          ".pytest_cache/*",
          "venv/*",
          ".venv/*",
          "*.egg-info/*"
        ];
      
      case 'java':
        return [
          ...common,
          "*.class",
          "target/*",
          "*.jar",
          "*.war",
          ".gradle/*",
          "build/*"
        ];
      
      case 'rust':
        return [
          ...common,
          "target/*",
          "Cargo.lock"
        ];
      
      case 'go':
        return [
          ...common,
          "go.sum",
          "vendor/*"
        ];
      
      default:
        return common;
    }
  }
}
