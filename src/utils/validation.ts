import * as vscode from "vscode";
import { getSupportedModels, getVariants } from "../models/modelVariants";
import { getAllLanguages } from "../models/language";

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
  suggestion?: string;
}

/**
 * Utility class for validating user inputs and configuration
 */
export class ValidationUtils {
  /**
   * Validate API key format
   */
  static validateApiKey(apiKey: string, modelName: string): ValidationResult {
    if (!apiKey || apiKey.trim().length === 0) {
      return {
        isValid: false,
        error: "API key cannot be empty",
        suggestion: "Please provide a valid API key",
      };
    }

    // Remove whitespace
    const trimmedKey = apiKey.trim();

    // Basic format validation based on model
    switch (modelName.toLowerCase()) {
      case "openai":
        if (!trimmedKey.startsWith("sk-")) {
          return {
            isValid: false,
            error: 'OpenAI API key should start with "sk-"',
            suggestion: "Please check your OpenAI API key format",
          };
        }
        if (trimmedKey.length < 20) {
          return {
            isValid: false,
            error: "OpenAI API key appears to be too short",
            suggestion: "Please verify your complete API key",
          };
        }
        break;

      case "claude":
      case "anthropic":
        if (!trimmedKey.startsWith("sk-ant-")) {
          return {
            isValid: false,
            error: 'Claude API key should start with "sk-ant-"',
            suggestion: "Please check your Anthropic API key format",
          };
        }
        break;

      case "gemini":
        if (trimmedKey.length < 30) {
          return {
            isValid: false,
            error: "Gemini API key appears to be too short",
            suggestion: "Please verify your complete Google AI API key",
          };
        }
        break;

      case "grok":
        if (!trimmedKey.startsWith("xai-")) {
          return {
            isValid: false,
            error: 'Grok API key should start with "xai-"',
            suggestion: "Please check your xAI API key format",
          };
        }
        break;

      case "github":
        if (!trimmedKey.startsWith("github_pat_")) {
          return {
            isValid: false,
            error: 'GitHub API key should start with "github_pat_"',
            suggestion: "Please check your GitHub personal access token format",
          };
        }
        break;

      case "deepseek":
        if (!trimmedKey.startsWith("sk-")) {
          return {
            isValid: false,
            error: 'DeepSeek API key should start with "sk-"',
            suggestion: "Please check your DeepSeek API key format",
          };
        }
        break;

      case "ollama":
        // Ollama doesn't require API key
        return { isValid: true };

      default:
        // For unknown models, just check it's not empty
        break;
    }

    // Check for common issues
    if (trimmedKey.includes(" ")) {
      return {
        isValid: false,
        error: "API key contains spaces",
        suggestion: "Please remove any spaces from your API key",
      };
    }

    if (trimmedKey !== apiKey) {
      return {
        isValid: false,
        error: "API key has leading or trailing whitespace",
        suggestion: "Please remove any extra spaces from your API key",
      };
    }

    return { isValid: true };
  }

  /**
   * Validate model name
   */
  static validateModel(modelName: string): ValidationResult {
    if (!modelName || modelName.trim().length === 0) {
      return {
        isValid: false,
        error: "Model name cannot be empty",
        suggestion: "Please select a valid AI model",
      };
    }

    const supportedModels = getSupportedModels();
    if (!supportedModels.includes(modelName)) {
      return {
        isValid: false,
        error: `Unsupported model: ${modelName}`,
        suggestion: `Supported models are: ${supportedModels.join(", ")}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate model variant
   */
  static validateModelVariant(
    modelName: string,
    variant: string
  ): ValidationResult {
    const modelValidation = this.validateModel(modelName);
    if (!modelValidation.isValid) {
      return modelValidation;
    }

    if (!variant || variant.trim().length === 0) {
      return {
        isValid: false,
        error: "Model variant cannot be empty",
        suggestion: "Please select a valid model variant",
      };
    }

    const supportedVariants = getVariants(modelName);
    const variantNames = supportedVariants.map((v) => v.name);
    if (!variantNames.includes(variant)) {
      return {
        isValid: false,
        error: `Unsupported variant "${variant}" for model ${modelName}`,
        suggestion: `Supported variants for ${modelName} are: ${variantNames.join(
          ", "
        )}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate language code
   */
  static validateLanguage(languageCode: string): ValidationResult {
    if (!languageCode || languageCode.trim().length === 0) {
      return {
        isValid: false,
        error: "Language code cannot be empty",
        suggestion: "Please select a valid language",
      };
    }

    const allLanguages = getAllLanguages();
    const language = allLanguages.find((lang) => lang.code === languageCode);

    if (!language) {
      const availableCodes = allLanguages.map((lang) => lang.code).join(", ");
      return {
        isValid: false,
        error: `Unsupported language code: ${languageCode}`,
        suggestion: `Supported language codes are: ${availableCodes}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate commit message
   */
  static validateCommitMessage(message: string): ValidationResult {
    if (!message || message.trim().length === 0) {
      return {
        isValid: false,
        error: "Commit message cannot be empty",
        suggestion: "Please provide a commit message",
      };
    }

    const trimmedMessage = message.trim();

    // Check minimum length
    if (trimmedMessage.length < 10) {
      return {
        isValid: false,
        error: "Commit message is too short",
        suggestion:
          "Please provide a more descriptive commit message (at least 10 characters)",
      };
    }

    // Check maximum length for first line
    const firstLine = trimmedMessage.split("\n")[0];
    if (firstLine.length > 72) {
      return {
        isValid: false,
        error: "First line of commit message is too long",
        suggestion: "Please keep the first line under 72 characters",
      };
    }

    // Check for conventional commit format (warning, not error)
    const conventionalCommitPattern =
      /^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?:\s*.+/;
    if (!conventionalCommitPattern.test(firstLine)) {
      return {
        isValid: true, // Still valid, just a warning
        suggestion:
          'Consider using conventional commit format (e.g., "feat: add new feature")',
      };
    }

    return { isValid: true };
  }

  /**
   * Validate URL format
   */
  static validateUrl(url: string, name: string = "URL"): ValidationResult {
    if (!url || url.trim().length === 0) {
      return {
        isValid: false,
        error: `${name} cannot be empty`,
        suggestion: `Please provide a valid ${name.toLowerCase()}`,
      };
    }

    try {
      new URL(url);
      return { isValid: true };
    } catch {
      return {
        isValid: false,
        error: `Invalid ${name.toLowerCase()} format`,
        suggestion: `Please provide a valid ${name.toLowerCase()} (e.g., https://example.com)`,
      };
    }
  }

  /**
   * Validate workspace folder
   */
  static validateWorkspaceFolder(): ValidationResult {
    const workspaceFolders = vscode.workspace.workspaceFolders;

    if (!workspaceFolders || workspaceFolders.length === 0) {
      return {
        isValid: false,
        error: "No workspace folder is open",
        suggestion: "Please open a folder or workspace in VS Code",
      };
    }

    return { isValid: true };
  }

  /**
   * Validate Git repository
   */
  static async validateGitRepository(
    folderPath: string
  ): Promise<ValidationResult> {
    try {
      const fs = require("fs");
      const path = require("path");

      // Check if .git directory exists
      const gitPath = path.join(folderPath, ".git");
      if (!fs.existsSync(gitPath)) {
        return {
          isValid: false,
          error: "Not a Git repository",
          suggestion:
            'Please initialize a Git repository with "git init" or open a Git repository',
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: "Error checking Git repository",
        suggestion:
          "Please ensure the folder is accessible and contains a valid Git repository",
      };
    }
  }

  /**
   * Show validation error to user
   */
  static showValidationError(result: ValidationResult, context?: string): void {
    if (!result.isValid && result.error) {
      const contextMsg = context ? ` ${context}` : "";
      let message = `Validation error${contextMsg}: ${result.error}`;

      if (result.suggestion) {
        message += `\n\nSuggestion: ${result.suggestion}`;
      }

      vscode.window.showErrorMessage(message);
    }
  }

  /**
   * Show validation warning to user
   */
  static showValidationWarning(
    result: ValidationResult,
    context?: string
  ): void {
    if (result.isValid && result.suggestion) {
      const contextMsg = context ? ` ${context}` : "";
      const message = `${contextMsg}: ${result.suggestion}`;
      vscode.window.showWarningMessage(message);
    }
  }

  /**
   * Validate configuration completeness
   */
  static async validateConfiguration(
    configManager: any
  ): Promise<ValidationResult> {
    try {
      const config = await configManager.getConfigSummary();

      // Check if default model is set
      if (!config.defaultModel) {
        return {
          isValid: false,
          error: "No default model configured",
          suggestion: "Please set a default AI model in the extension settings",
        };
      }

      // Check if model has API key (except Ollama)
      if (
        config.defaultModel !== "ollama" &&
        !config.hasApiKeys[config.defaultModel]
      ) {
        return {
          isValid: false,
          error: `No API key configured for ${config.defaultModel}`,
          suggestion: `Please set an API key for ${config.defaultModel} in the extension settings`,
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: "Error validating configuration",
        suggestion: "Please check your extension settings",
      };
    }
  }
}
