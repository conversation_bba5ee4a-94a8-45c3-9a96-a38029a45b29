import * as vscode from "vscode";
import { simpleGit, SimpleGit, StatusResult } from "simple-git";
import * as path from "path";
import * as fs from "fs";

/**
 * Utility class for Git operations in VS Code extension
 * Based on the GitUtils from the reference Dart implementation
 */
export class GitUtils {
  private git: SimpleGit;
  private workspaceRoot: string;

  constructor(workspaceRoot: string) {
    this.workspaceRoot = workspaceRoot;
    this.git = simpleGit(workspaceRoot);
  }

  /**
   * Check if the current directory is a Git repository
   */
  async isGitRepository(): Promise<boolean> {
    try {
      await this.git.status();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Find Git repositories in subfolders (1 level down)
   */
  async findGitReposInSubfolders(): Promise<string[]> {
    const gitRepos: string[] = [];

    try {
      const items = await fs.promises.readdir(this.workspaceRoot, {
        withFileTypes: true,
      });
      const subdirs = items.filter((item) => item.isDirectory());

      for (const subdir of subdirs) {
        const subdirPath = path.join(this.workspaceRoot, subdir.name);
        const gitDir = path.join(subdirPath, ".git");

        try {
          await fs.promises.access(gitDir);
          // Verify it's actually a git repo
          const subGit = simpleGit(subdirPath);
          await subGit.status();
          gitRepos.push(subdirPath);
        } catch (error) {
          // Not a git repo or can't access
          continue;
        }
      }
    } catch (error) {
      console.error("Error finding git repos in subfolders:", error);
    }

    return gitRepos;
  }

  /**
   * Get the diff of staged changes
   */
  async getStagedDiff(folderPath?: string): Promise<string> {
    try {
      const git = folderPath ? simpleGit(folderPath) : this.git;
      const diff = await git.diff(["--cached"]);
      return diff;
    } catch (error) {
      console.error("Error getting staged diff:", error);
      return "";
    }
  }

  /**
   * Get the diff of unstaged changes
   */
  async getUnstagedDiff(folderPath?: string): Promise<string> {
    try {
      const git = folderPath ? simpleGit(folderPath) : this.git;
      const diff = await git.diff();
      return diff;
    } catch (error) {
      console.error("Error getting unstaged diff:", error);
      return "";
    }
  }

  /**
   * Check if there are staged changes
   */
  async hasStagedChanges(folderPath?: string): Promise<boolean> {
    try {
      const git = folderPath ? simpleGit(folderPath) : this.git;
      const status: StatusResult = await git.status();
      return status.staged.length > 0;
    } catch (error) {
      console.error("Error checking staged changes:", error);
      return false;
    }
  }

  /**
   * Check if there are unstaged changes
   */
  async hasUnstagedChanges(folderPath?: string): Promise<boolean> {
    try {
      const git = folderPath ? simpleGit(folderPath) : this.git;
      const status: StatusResult = await git.status();
      return status.modified.length > 0 || status.not_added.length > 0;
    } catch (error) {
      console.error("Error checking unstaged changes:", error);
      return false;
    }
  }

  /**
   * Returns a list of folder paths that have staged changes
   */
  async foldersWithStagedChanges(folders: string[]): Promise<string[]> {
    const result: string[] = [];

    for (const folder of folders) {
      if (await this.hasStagedChanges(folder)) {
        result.push(folder);
      }
    }

    return result;
  }

  /**
   * Returns a list of folder paths that have unstaged changes
   */
  async foldersWithUnstagedChanges(folders: string[]): Promise<string[]> {
    const result: string[] = [];

    for (const folder of folders) {
      if (await this.hasUnstagedChanges(folder)) {
        result.push(folder);
      }
    }

    return result;
  }

  /**
   * Stage all unstaged changes and return the number of files staged
   */
  async stageAllUnstagedFilesAndCount(folderPath?: string): Promise<number> {
    try {
      const git = folderPath ? simpleGit(folderPath) : this.git;

      // Get status before staging
      const beforeStatus: StatusResult = await git.status();
      const beforeStaged = beforeStatus.staged.length;

      // Stage all changes
      await git.add(".");

      // Get status after staging
      const afterStatus: StatusResult = await git.status();
      const afterStaged = afterStatus.staged.length;

      return afterStaged - beforeStaged;
    } catch (error) {
      console.error("Error staging files:", error);
      throw new Error(`Failed to stage files: ${error}`);
    }
  }

  /**
   * Run git commit with message
   */
  async runGitCommit(options: {
    message: string;
    autoPush?: boolean;
    folderPath?: string;
    githubPAT?: string;
  }): Promise<void> {
    try {
      const git = options.folderPath ? simpleGit(options.folderPath) : this.git;

      // Commit the changes
      await git.commit(options.message);

      const folderName = options.folderPath
        ? path.basename(options.folderPath)
        : "";
      const prefix = folderName ? `[${folderName}] ` : "";

      if (!options.autoPush) {
        vscode.window.showInformationMessage(`${prefix}Commit successful! 🎉`);
      } else {
        // Auto-push if requested
        vscode.window.showInformationMessage(
          `${prefix}Commit successful! Syncing with remote branch...`
        );

        try {
          await this.pushToRemote(git, options.githubPAT, prefix);
        } catch (pushError) {
          vscode.window.showErrorMessage(
            `${prefix}Commit successful but push failed: ${pushError}`
          );
        }
      }
    } catch (error) {
      console.error("Error during git commit:", error);
      throw new Error(`Error during git commit: ${error}`);
    }
  }

  /**
   * Commit and push changes with GitHub PAT authentication
   */
  async runGitCommitAndPush(options: {
    message: string;
    folderPath?: string;
    githubPAT?: string;
  }): Promise<void> {
    try {
      const git = options.folderPath ? simpleGit(options.folderPath) : this.git;

      // Commit the changes
      await git.commit(options.message);

      const folderName = options.folderPath
        ? path.basename(options.folderPath)
        : "";
      const prefix = folderName ? `[${folderName}] ` : "";

      vscode.window.showInformationMessage(
        `${prefix}Commit successful! Pushing to remote...`
      );

      try {
        await this.pushToRemote(git, options.githubPAT, prefix);
      } catch (pushError) {
        vscode.window.showErrorMessage(
          `${prefix}Commit successful but push failed: ${pushError}`
        );
        throw pushError;
      }
    } catch (error) {
      console.error("Error during git commit and push:", error);
      throw new Error(`Error during git commit and push: ${error}`);
    }
  }

  /**
   * Push to remote with optional GitHub PAT authentication
   */
  private async pushToRemote(
    git: SimpleGit,
    githubPAT?: string,
    prefix: string = ""
  ): Promise<void> {
    // Get current branch
    const status = await git.status();
    const currentBranch = status.current;

    if (!currentBranch) {
      throw new Error("No current branch found");
    }

    // Get remote information
    const remotes = await git.getRemotes(true);
    const remoteName = remotes.length > 0 ? remotes[0].name : "origin";
    const remoteUrl = remotes.length > 0 ? remotes[0].refs.push : "";

    // Check if this is a GitHub repository and if we need PAT authentication
    if (githubPAT && remoteUrl && remoteUrl.includes("github.com")) {
      // Configure remote URL with PAT for this push
      const originalUrl = remoteUrl;
      let authenticatedUrl = "";

      if (remoteUrl.startsWith("https://github.com/")) {
        // HTTPS URL - inject PAT
        authenticatedUrl = remoteUrl.replace(
          "https://github.com/",
          `https://${githubPAT}@github.com/`
        );
      } else if (remoteUrl.startsWith("**************:")) {
        // SSH URL - convert to HTTPS with PAT
        const repoPath = remoteUrl
          .replace("**************:", "")
          .replace(".git", "");
        authenticatedUrl = `https://${githubPAT}@github.com/${repoPath}.git`;
      } else {
        // Already has authentication or unknown format
        authenticatedUrl = remoteUrl;
      }

      try {
        // Temporarily set the remote URL with PAT
        await git.remote(["set-url", remoteName, authenticatedUrl]);

        // Push to remote
        await git.push(remoteName, currentBranch);

        // Restore original URL
        await git.remote(["set-url", remoteName, originalUrl]);

        vscode.window.showInformationMessage(
          `${prefix}Pushed to ${remoteName}/${currentBranch} successfully! 🎉`
        );
      } catch (error) {
        // Restore original URL even if push failed
        try {
          await git.remote(["set-url", remoteName, originalUrl]);
        } catch (restoreError) {
          console.error("Failed to restore original remote URL:", restoreError);
        }
        throw error;
      }
    } else {
      // Standard push without PAT
      await git.push(remoteName, currentBranch);
      vscode.window.showInformationMessage(
        `${prefix}Pushed to ${remoteName}/${currentBranch} successfully! 🎉`
      );
    }
  }

  /**
   * Check if the current repository is a GitHub repository
   */
  async isGitHubRepository(folderPath?: string): Promise<boolean> {
    try {
      const git = folderPath ? simpleGit(folderPath) : this.git;
      const remotes = await git.getRemotes(true);

      for (const remote of remotes) {
        const url = remote.refs.push || remote.refs.fetch;
        if (url && url.includes("github.com")) {
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error("Error checking if repository is GitHub:", error);
      return false;
    }
  }

  /**
   * Remove Markdown-style code block markers from a string
   * Useful for cleaning AI-generated commit messages
   */
  static stripMarkdownCodeBlocks(input: string): string {
    const codeBlockPattern = /^```(\w+)?\n?|```$/gm;
    return input.replace(codeBlockPattern, "").trim();
  }

  /**
   * Get the current working directory path
   */
  getCurrentDirectoryPath(): string {
    return this.workspaceRoot;
  }
}
