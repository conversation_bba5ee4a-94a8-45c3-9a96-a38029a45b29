import { Language } from "../models/language";

/**
 * Interface for a diff chunk
 */
export interface DiffChunk {
  content: string;
  files: string[];
  chunkIndex: number;
  totalChunks: number;
  estimatedTokens: number;
}

/**
 * Interface for chunking options
 */
export interface ChunkingOptions {
  maxTokensPerChunk: number;
  maxFilesPerChunk: number;
  preserveFileIntegrity: boolean;
}

/**
 * Result of processing chunked commits
 */
export interface ChunkedCommitResult {
  combinedMessage: string;
  individualMessages: string[];
  totalChunks: number;
  processedSuccessfully: number;
}

/**
 * Utility class for splitting large diffs into manageable chunks
 * and combining the resulting commit messages
 */
export class DiffChunker {
  private static readonly DEFAULT_MAX_TOKENS = 3000;
  private static readonly DEFAULT_MAX_FILES = 10;
  private static readonly TOKENS_PER_CHAR_ESTIMATE = 0.25; // Rough estimate: 4 chars per token

  /**
   * Estimate the number of tokens in a text string
   */
  static estimateTokens(text: string): number {
    return Math.ceil(text.length * this.TOKENS_PER_CHAR_ESTIMATE);
  }

  /**
   * Check if a diff needs to be chunked based on token limits
   */
  static needsChunking(diff: string, maxTokens: number = this.DEFAULT_MAX_TOKENS): boolean {
    const estimatedTokens = this.estimateTokens(diff);
    return estimatedTokens > maxTokens;
  }

  /**
   * Parse a git diff into individual file changes
   */
  static parseGitDiff(diff: string): Array<{ filename: string; content: string }> {
    const files: Array<{ filename: string; content: string }> = [];
    const filePattern = /^diff --git a\/(.+?) b\/(.+?)$/gm;
    
    let match;
    let lastIndex = 0;
    
    while ((match = filePattern.exec(diff)) !== null) {
      // Add previous file if exists
      if (lastIndex > 0) {
        const previousContent = diff.substring(lastIndex, match.index);
        if (files.length > 0) {
          files[files.length - 1].content += previousContent;
        }
      }
      
      // Start new file
      const filename = match[1];
      files.push({
        filename,
        content: match[0] + '\n'
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add content for the last file
    if (files.length > 0 && lastIndex < diff.length) {
      files[files.length - 1].content += diff.substring(lastIndex);
    }
    
    // If no files found, treat entire diff as one file
    if (files.length === 0 && diff.trim()) {
      files.push({
        filename: 'unknown',
        content: diff
      });
    }
    
    return files;
  }

  /**
   * Split a large diff into smaller chunks
   */
  static chunkDiff(diff: string, options: Partial<ChunkingOptions> = {}): DiffChunk[] {
    const opts: ChunkingOptions = {
      maxTokensPerChunk: options.maxTokensPerChunk || this.DEFAULT_MAX_TOKENS,
      maxFilesPerChunk: options.maxFilesPerChunk || this.DEFAULT_MAX_FILES,
      preserveFileIntegrity: options.preserveFileIntegrity ?? true
    };

    const files = this.parseGitDiff(diff);
    const chunks: DiffChunk[] = [];
    
    if (files.length === 0) {
      return chunks;
    }

    let currentChunk: { files: Array<{ filename: string; content: string }>; tokens: number } = {
      files: [],
      tokens: 0
    };

    for (const file of files) {
      const fileTokens = this.estimateTokens(file.content);
      
      // If a single file exceeds the token limit and we're preserving file integrity
      if (fileTokens > opts.maxTokensPerChunk && opts.preserveFileIntegrity) {
        // Finish current chunk if it has content
        if (currentChunk.files.length > 0) {
          chunks.push(this.createChunk(currentChunk.files, chunks.length, 0));
          currentChunk = { files: [], tokens: 0 };
        }
        
        // Add the large file as its own chunk
        chunks.push(this.createChunk([file], chunks.length, 0));
        continue;
      }
      
      // Check if adding this file would exceed limits
      const wouldExceedTokens = currentChunk.tokens + fileTokens > opts.maxTokensPerChunk;
      const wouldExceedFiles = currentChunk.files.length >= opts.maxFilesPerChunk;
      
      if ((wouldExceedTokens || wouldExceedFiles) && currentChunk.files.length > 0) {
        // Finish current chunk
        chunks.push(this.createChunk(currentChunk.files, chunks.length, 0));
        currentChunk = { files: [], tokens: 0 };
      }
      
      // Add file to current chunk
      currentChunk.files.push(file);
      currentChunk.tokens += fileTokens;
    }
    
    // Add final chunk if it has content
    if (currentChunk.files.length > 0) {
      chunks.push(this.createChunk(currentChunk.files, chunks.length, 0));
    }
    
    // Update total chunks count
    chunks.forEach((chunk, index) => {
      chunk.chunkIndex = index + 1;
      chunk.totalChunks = chunks.length;
    });
    
    return chunks;
  }

  /**
   * Create a DiffChunk from file data
   */
  private static createChunk(
    files: Array<{ filename: string; content: string }>,
    index: number,
    total: number
  ): DiffChunk {
    const content = files.map(f => f.content).join('\n');
    const filenames = files.map(f => f.filename);
    
    return {
      content,
      files: filenames,
      chunkIndex: index + 1,
      totalChunks: total,
      estimatedTokens: this.estimateTokens(content)
    };
  }

  /**
   * Combine multiple commit messages into a single coherent message
   */
  static combineCommitMessages(messages: string[], language: Language): string {
    if (messages.length === 0) {
      return "";
    }
    
    if (messages.length === 1) {
      return messages[0];
    }

    // Extract commit types and descriptions from each message
    const commitParts = messages.map(msg => {
      const match = msg.match(/^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert):\s*([✨🐛📚💄♻️🧪🔧⚡👷📦⏪])\s*(.+)$/);
      if (match) {
        return {
          type: match[1],
          emoji: match[2],
          description: match[3]
        };
      }
      return {
        type: 'chore',
        emoji: '🔧',
        description: msg.replace(/^[^:]*:\s*[^\s]*\s*/, '') // Remove type and emoji
      };
    });

    // Determine the most appropriate overall type
    const types = commitParts.map(p => p.type);
    const typeFrequency = types.reduce((acc, type) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const dominantType = Object.entries(typeFrequency)
      .sort(([,a], [,b]) => b - a)[0][0];
    
    // Get corresponding emoji
    const typeEmojiMap: Record<string, string> = {
      feat: '✨', fix: '🐛', docs: '📚', style: '💄',
      refactor: '♻️', test: '🧪', chore: '🔧', perf: '⚡',
      ci: '👷', build: '📦', revert: '⏪'
    };
    
    const emoji = typeEmojiMap[dominantType] || '🔧';
    
    // Create combined description
    const descriptions = commitParts.map(p => p.description);
    const uniqueDescriptions = [...new Set(descriptions)];
    
    let combinedDescription: string;
    if (uniqueDescriptions.length === 1) {
      combinedDescription = uniqueDescriptions[0];
    } else if (uniqueDescriptions.length <= 3) {
      combinedDescription = uniqueDescriptions.join(', ');
    } else {
      combinedDescription = `Multiple changes across ${messages.length} areas`;
    }
    
    // Ensure the combined message stays within length limits
    const maxLength = 72 - `${dominantType}: ${emoji} `.length;
    if (combinedDescription.length > maxLength) {
      combinedDescription = combinedDescription.substring(0, maxLength - 3) + '...';
    }
    
    return `${dominantType}: ${emoji} ${combinedDescription}`;
  }

  /**
   * Generate a summary of the chunking process
   */
  static generateChunkingSummary(chunks: DiffChunk[]): string {
    const totalFiles = chunks.reduce((sum, chunk) => sum + chunk.files.length, 0);
    const totalTokens = chunks.reduce((sum, chunk) => sum + chunk.estimatedTokens, 0);
    
    return `Split into ${chunks.length} chunks covering ${totalFiles} files (~${totalTokens} tokens)`;
  }
}
