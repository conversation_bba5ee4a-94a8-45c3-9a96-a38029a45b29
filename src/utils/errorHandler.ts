import * as vscode from "vscode";

/**
 * Base class for all API-related exceptions
 */
export class ApiException extends Error {
  constructor(
    message: string,
    public statusCode: number = 0,
    public errorType?: string,
    public errorCode?: string,
    public requestId?: string,
    public retryAfter?: number
  ) {
    super(message);
    this.name = this.constructor.name;
  }

  /**
   * Whether this error is retryable
   */
  get isRetryable(): boolean {
    return this.statusCode >= 500 || this.statusCode === 429;
  }

  /**
   * Whether this error is due to rate limiting
   */
  get isRateLimited(): boolean {
    return this.statusCode === 429;
  }

  /**
   * Whether this error is due to authentication issues
   */
  get isAuthenticationError(): boolean {
    return this.statusCode === 401;
  }

  /**
   * Whether this error is a server error
   */
  get isServerError(): boolean {
    return this.statusCode >= 500;
  }

  /**
   * Get a user-friendly error message with recovery suggestions
   */
  get userFriendlyMessage(): string {
    switch (this.statusCode) {
      case 400:
        return `Invalid request: ${this.message}\nPlease check your request parameters and try again.`;
      case 401:
        return `Authentication failed: ${this.message}\nPlease check your API key and ensure it's valid.`;
      case 403:
        return `Permission denied: ${this.message}\nPlease check your API key permissions or account status.`;
      case 404:
        return `Resource not found: ${this.message}\nPlease check the model name or endpoint URL.`;
      case 413:
        return `Request too large: ${this.message}\nPlease reduce the size of your request (shorter diff or prompt).`;
      case 429:
        const retryMsg = this.retryAfter
          ? ` Please wait ${this.retryAfter} seconds before retrying.`
          : " Please wait before retrying.";
        return `Rate limit exceeded: ${this.message}${retryMsg}`;
      case 500:
        return `Server error: ${this.message}\nThis is a temporary issue. Please try again later.`;
      case 503:
        return `Service unavailable: ${this.message}\nThe service is temporarily down. Please try again later.`;
      case 529:
        return `Service overloaded: ${this.message}\nThe service is experiencing high traffic. Please try again later.`;
      default:
        return `API error (${this.statusCode}): ${this.message}`;
    }
  }
}

/**
 * Exception for authentication-related errors (401)
 */
export class AuthenticationException extends ApiException {
  constructor(message: string, errorType?: string, requestId?: string) {
    super(message, 401, errorType, undefined, requestId);
  }

  get userFriendlyMessage(): string {
    return (
      `Authentication failed: ${this.message}\n` +
      "Solutions:\n" +
      "• Check that your API key is correct and valid\n" +
      "• Ensure your API key has not expired\n" +
      "• Verify you're using the correct API key for this service\n" +
      "• Make sure your API key is properly formatted (no extra spaces)"
    );
  }
}

/**
 * Exception for rate limiting errors (429)
 */
export class RateLimitException extends ApiException {
  constructor(
    message: string,
    retryAfter?: number,
    errorType?: string,
    requestId?: string
  ) {
    super(message, 429, errorType, undefined, requestId, retryAfter);
  }
}

/**
 * Exception for server errors (5xx)
 */
export class ServerException extends ApiException {
  constructor(
    message: string,
    statusCode: number = 500,
    errorType?: string,
    requestId?: string
  ) {
    super(message, statusCode, errorType, undefined, requestId);
  }
}

/**
 * Exception for timeout errors
 */
export class TimeoutException extends ApiException {
  constructor(message: string, requestId?: string) {
    super(message, 408, "timeout", undefined, requestId);
  }
}

/**
 * Exception for permission errors (403)
 */
export class PermissionException extends ApiException {
  constructor(message: string, errorType?: string, requestId?: string) {
    super(message, 403, errorType, undefined, requestId);
  }
}

/**
 * Exception for service overload errors (529)
 */
export class ServiceOverloadedException extends ApiException {
  constructor(message: string, errorType?: string, requestId?: string) {
    super(message, 529, errorType, undefined, requestId);
  }
}

/**
 * Utility class for handling errors in commands
 */
export class ErrorHandler {
  /**
   * Handle API errors with appropriate user feedback
   */
  static handleApiError(error: ApiException, context?: string): void {
    console.error("API Error:", error);

    const contextMsg = context ? ` while ${context}` : "";
    const title = `Error${contextMsg}`;

    // Show user-friendly error message
    vscode.window.showErrorMessage(`${title}: ${error.userFriendlyMessage}`);
  }

  /**
   * Handle errors with retry suggestions
   */
  static handleErrorWithRetry(
    error: ApiException,
    context?: string,
    showRetryInfo: boolean = true
  ): void {
    this.handleApiError(error, context);

    if (showRetryInfo && error.isRetryable) {
      const retryOptions: string[] = ["Retry"];

      if (error.isRateLimited && error.retryAfter) {
        vscode.window.showInformationMessage(
          `This error is retryable. Wait ${error.retryAfter} seconds before retrying.`,
          ...retryOptions
        );
      } else if (error.isRateLimited) {
        vscode.window.showInformationMessage(
          "This error is retryable. Wait a few minutes before retrying.",
          ...retryOptions
        );
      } else if (error.isServerError) {
        vscode.window.showInformationMessage(
          "This error is retryable. Wait a few minutes and try again.",
          ...retryOptions
        );
      }
    }
  }

  /**
   * Handle errors with fallback options
   */
  static handleErrorWithFallback(
    error: ApiException,
    context?: string,
    fallbackOptions?: string[]
  ): void {
    this.handleApiError(error, context);

    if (fallbackOptions && fallbackOptions.length > 0) {
      const message =
        "You can try these alternatives:\n" +
        fallbackOptions.map((option) => `• ${option}`).join("\n");
      vscode.window.showInformationMessage(message);
    }
  }

  /**
   * Handle general errors (non-API)
   */
  static handleGeneralError(error: Error, context?: string): void {
    console.error("General Error:", error);

    const contextMsg = context ? ` while ${context}` : "";
    vscode.window.showErrorMessage(`Error${contextMsg}: ${error.message}`);
  }

  /**
   * Check if we should suggest switching models
   */
  static shouldSuggestModelSwitch(error: ApiException): boolean {
    return (
      error.isAuthenticationError ||
      error.statusCode === 403 ||
      error.statusCode === 404 ||
      (error.isRateLimited && !error.retryAfter)
    );
  }

  /**
   * Get model switch suggestions based on error
   */
  static getModelSwitchSuggestions(error: ApiException): string[] {
    const suggestions: string[] = [];

    if (error.isAuthenticationError) {
      suggestions.push("Try a different AI model with a valid API key");
      suggestions.push(
        "Use Ollama for local AI generation (no API key required)"
      );
    } else if (error.statusCode === 403) {
      suggestions.push("Switch to a model you have access to");
      suggestions.push("Check your account permissions for this model");
    } else if (error.statusCode === 404) {
      suggestions.push("Use a different model variant that exists");
      suggestions.push("Check the model name spelling");
    } else if (error.isRateLimited) {
      suggestions.push("Switch to a different AI provider temporarily");
      suggestions.push("Use Ollama for unlimited local generation");
    }

    return suggestions;
  }

  /**
   * Parse error from HTTP response
   */
  static parseHttpError(error: any, provider: string): ApiException {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      const requestId = this.extractRequestId(error.response.headers);

      switch (status) {
        case 401:
          return new AuthenticationException(
            data?.error?.message || data?.message || "Invalid API key",
            data?.error?.type,
            requestId
          );
        case 403:
          return new PermissionException(
            data?.error?.message || data?.message || "Access forbidden",
            data?.error?.type,
            requestId
          );
        case 429:
          const retryAfter = error.response.headers["retry-after"]
            ? parseInt(error.response.headers["retry-after"])
            : undefined;
          return new RateLimitException(
            data?.error?.message || data?.message || "Rate limit exceeded",
            retryAfter,
            data?.error?.type,
            requestId
          );
        case 500:
        case 502:
        case 503:
          return new ServerException(
            data?.error?.message || data?.message || "Server error",
            status,
            data?.error?.type,
            requestId
          );
        case 529:
          return new ServiceOverloadedException(
            data?.error?.message || data?.message || "Service overloaded",
            data?.error?.type,
            requestId
          );
        default:
          return new ApiException(
            data?.error?.message || data?.message || "Unknown API error",
            status,
            data?.error?.type,
            data?.error?.code,
            requestId
          );
      }
    } else if (error.code === "ECONNREFUSED" || error.code === "ENOTFOUND") {
      return new ApiException(
        `Cannot connect to ${provider} API. Please check your internet connection.`,
        0,
        "connection_error"
      );
    } else if (error.code === "ETIMEDOUT") {
      return new TimeoutException(`Request to ${provider} API timed out`);
    } else {
      return new ApiException(
        error.message || "Unknown error",
        0,
        "unknown_error"
      );
    }
  }

  /**
   * Extract request ID from response headers
   */
  private static extractRequestId(headers: any): string | undefined {
    if (!headers) {
      return undefined;
    }

    // Common request ID header names
    const requestIdHeaders = [
      "x-request-id",
      "request-id",
      "x-trace-id",
      "trace-id",
      "cf-ray",
    ];

    for (const header of requestIdHeaders) {
      if (headers[header]) {
        return headers[header];
      }
    }

    return undefined;
  }
}
