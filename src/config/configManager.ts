import * as vscode from "vscode";
import {
  Language,
  Languages,
  parseLanguageString,
  languageToString,
} from "../models/language";
import { ValidationUtils } from "../utils/validation";
import { CommitGeneratorFactory } from "../models/commitGeneratorFactory";

/**
 * Configuration manager for GitWhisper extension
 * Handles storing and retrieving settings using VS Code's configuration system
 * Based on the ConfigManager from the reference Dart implementation
 */
export class ConfigManager {
  private static readonly EXTENSION_ID = "gitwhisper";

  /**
   * Get the VS Code configuration for GitWhisper
   */
  private getConfig(): vscode.WorkspaceConfiguration {
    return vscode.workspace.getConfiguration(ConfigManager.EXTENSION_ID);
  }

  /**
   * Get API key for the specified model from VS Code secrets
   */
  async getApiKey(model: string): Promise<string | undefined> {
    // For GitHub, redirect to PAT method
    if (model.toLowerCase() === "github") {
      return await this.getGitHubPAT();
    }

    const secretKey = `${
      ConfigManager.EXTENSION_ID
    }.apiKey.${model.toLowerCase()}`;
    try {
      const context = this.getExtensionContext();
      if (context) {
        return await context.secrets.get(secretKey);
      }
    } catch (error) {
      console.error(`Error getting API key for ${model}:`, error);
    }
    return undefined;
  }

  /**
   * Set API key for the specified model in VS Code secrets
   */
  async setApiKey(model: string, apiKey: string): Promise<void> {
    // For GitHub, redirect to PAT method
    if (model.toLowerCase() === "github") {
      await this.setGitHubPAT(apiKey);
      return;
    }

    // Validate API key format
    const validation = ValidationUtils.validateApiKey(apiKey, model);
    if (!validation.isValid) {
      ValidationUtils.showValidationError(validation, `for ${model}`);
      return;
    }

    const secretKey = `${
      ConfigManager.EXTENSION_ID
    }.apiKey.${model.toLowerCase()}`;
    try {
      const context = this.getExtensionContext();
      if (context) {
        await context.secrets.store(secretKey, apiKey);
        vscode.window.showInformationMessage(
          `API key for ${model} saved successfully.`
        );
      }
    } catch (error) {
      console.error(`Error setting API key for ${model}:`, error);
      vscode.window.showErrorMessage(`Failed to save API key for ${model}.`);
    }
  }

  /**
   * Remove API key for the specified model from VS Code secrets
   */
  async removeApiKey(model: string): Promise<void> {
    // For GitHub, redirect to PAT removal method
    if (model.toLowerCase() === "github") {
      await this.removeGitHubPAT();
      return;
    }

    const secretKey = `${
      ConfigManager.EXTENSION_ID
    }.apiKey.${model.toLowerCase()}`;
    try {
      const context = this.getExtensionContext();
      if (context) {
        await context.secrets.delete(secretKey);
        vscode.window.showInformationMessage(
          `API key for ${model} removed successfully.`
        );
      }
    } catch (error) {
      console.error(`Error removing API key for ${model}:`, error);
      vscode.window.showErrorMessage(`Failed to remove API key for ${model}.`);
    }
  }

  /**
   * Get default model and variant
   */
  getDefaultModelAndVariant(): [string, string] | undefined {
    const config = this.getConfig();
    const model = config.get<string>("defaultModel");
    const variant = config.get<string>("defaultVariant");

    if (model && variant) {
      return [model, variant];
    }
    return undefined;
  }

  /**
   * Set default model and variant
   */
  async setDefaults(model: string, modelVariant: string): Promise<void> {
    const config = this.getConfig();
    try {
      await config.update(
        "defaultModel",
        model,
        vscode.ConfigurationTarget.Global
      );
      await config.update(
        "defaultVariant",
        modelVariant,
        vscode.ConfigurationTarget.Global
      );
      vscode.window.showInformationMessage(
        `Default model set to ${model} (${modelVariant})`
      );
    } catch (error) {
      console.error("Error setting defaults:", error);
      vscode.window.showErrorMessage("Failed to save default model settings.");
    }
  }

  /**
   * Clear default model and variant
   */
  async clearDefaults(): Promise<void> {
    const config = this.getConfig();
    try {
      await config.update(
        "defaultModel",
        undefined,
        vscode.ConfigurationTarget.Global
      );
      await config.update(
        "defaultVariant",
        undefined,
        vscode.ConfigurationTarget.Global
      );
      vscode.window.showInformationMessage("Default model settings cleared.");
    } catch (error) {
      console.error("Error clearing defaults:", error);
      vscode.window.showErrorMessage("Failed to clear default model settings.");
    }
  }

  /**
   * Get Ollama base URL
   */
  getOllamaBaseURL(): string {
    const config = this.getConfig();
    return config.get<string>("ollamaBaseUrl") || "http://localhost:11434";
  }

  /**
   * Set Ollama base URL
   */
  async setOllamaBaseURL(baseUrl: string): Promise<void> {
    const config = this.getConfig();
    try {
      await config.update(
        "ollamaBaseUrl",
        baseUrl,
        vscode.ConfigurationTarget.Global
      );
      vscode.window.showInformationMessage(`Ollama base URL set to ${baseUrl}`);
    } catch (error) {
      console.error("Error setting Ollama base URL:", error);
      vscode.window.showErrorMessage("Failed to save Ollama base URL.");
    }
  }

  /**
   * Get the language for commit messages
   */
  getWhisperLanguage(): Language {
    const config = this.getConfig();
    const languageString = config.get<string>("language") || "en;US";
    return parseLanguageString(languageString);
  }

  /**
   * Set the language for commit messages
   */
  async setWhisperLanguage(language: Language): Promise<void> {
    const config = this.getConfig();
    const languageString = languageToString(language);
    try {
      await config.update(
        "language",
        languageString,
        vscode.ConfigurationTarget.Global
      );
      vscode.window.showInformationMessage(
        `Commit language set to ${language.displayName}`
      );
    } catch (error) {
      console.error("Error setting language:", error);
      vscode.window.showErrorMessage("Failed to save language setting.");
    }
  }

  /**
   * Get custom Ollama variant
   */
  getCustomOllamaVariant(): string | undefined {
    const config = this.getConfig();
    return config.get<string>("customOllamaVariant");
  }

  /**
   * Set custom Ollama variant
   */
  async setCustomOllamaVariant(variant: string): Promise<void> {
    const config = this.getConfig();
    await config.update(
      "customOllamaVariant",
      variant,
      vscode.ConfigurationTarget.Global
    );
  }

  /**
   * Get always add setting
   */
  shouldAlwaysAdd(): boolean {
    const config = this.getConfig();
    return config.get<boolean>("alwaysAdd") || false;
  }

  /**
   * Set always add setting
   */
  async setAlwaysAdd(value: boolean): Promise<void> {
    const config = this.getConfig();
    try {
      await config.update(
        "alwaysAdd",
        value,
        vscode.ConfigurationTarget.Global
      );
      vscode.window.showInformationMessage(
        `Always add unstaged files: ${value ? "enabled" : "disabled"}`
      );
    } catch (error) {
      console.error("Error setting always add:", error);
      vscode.window.showErrorMessage("Failed to save always add setting.");
    }
  }

  /**
   * Get auto push setting
   */
  shouldAutoPush(): boolean {
    const config = this.getConfig();
    return config.get<boolean>("autoPush") || false;
  }

  /**
   * Set auto push setting
   */
  async setAutoPush(value: boolean): Promise<void> {
    const config = this.getConfig();
    try {
      await config.update("autoPush", value, vscode.ConfigurationTarget.Global);
      vscode.window.showInformationMessage(
        `Auto push commits: ${value ? "enabled" : "disabled"}`
      );
    } catch (error) {
      console.error("Error setting auto push:", error);
      vscode.window.showErrorMessage("Failed to save auto push setting.");
    }
  }

  /**
   * Get GitHub Personal Access Token
   */
  async getGitHubPAT(): Promise<string | undefined> {
    const secretKey = `${ConfigManager.EXTENSION_ID}.githubPAT`;
    try {
      const context = this.getExtensionContext();
      if (context) {
        return await context.secrets.get(secretKey);
      }
    } catch (error) {
      console.error("Error getting GitHub PAT:", error);
    }
    return undefined;
  }

  /**
   * Set GitHub Personal Access Token
   */
  async setGitHubPAT(token: string): Promise<void> {
    // Validate GitHub PAT format (basic validation)
    if (!token || token.length < 10) {
      vscode.window.showErrorMessage(
        "Invalid GitHub Personal Access Token format."
      );
      return;
    }

    const secretKey = `${ConfigManager.EXTENSION_ID}.githubPAT`;
    try {
      const context = this.getExtensionContext();
      if (context) {
        await context.secrets.store(secretKey, token);
        vscode.window.showInformationMessage(
          "GitHub Personal Access Token saved successfully."
        );
      }
    } catch (error) {
      console.error("Error setting GitHub PAT:", error);
      vscode.window.showErrorMessage(
        "Failed to save GitHub Personal Access Token."
      );
    }
  }

  /**
   * Remove GitHub Personal Access Token
   */
  async removeGitHubPAT(): Promise<void> {
    const secretKey = `${ConfigManager.EXTENSION_ID}.githubPAT`;
    try {
      const context = this.getExtensionContext();
      if (context) {
        await context.secrets.delete(secretKey);
        vscode.window.showInformationMessage(
          "GitHub Personal Access Token removed successfully."
        );
      }
    } catch (error) {
      console.error("Error removing GitHub PAT:", error);
      vscode.window.showErrorMessage(
        "Failed to remove GitHub Personal Access Token."
      );
    }
  }

  /**
   * Get environment API key for the specified model
   */
  getEnvironmentApiKey(modelName: string): string | undefined {
    const envVarMap: { [key: string]: string } = {
      claude: "ANTHROPIC_API_KEY",
      openai: "OPENAI_API_KEY",
      gemini: "GEMINI_API_KEY",
      grok: "GROK_API_KEY",
      llama: "LLAMA_API_KEY",
      deepseek: "DEEPSEEK_API_KEY",
      github: "GITHUB_TOKEN",
    };

    const envVar = envVarMap[modelName.toLowerCase()];
    return envVar ? process.env[envVar] : undefined;
  }

  /**
   * Get extension context (helper method)
   */
  private getExtensionContext(): vscode.ExtensionContext | undefined {
    // This will be set by the extension when it activates
    return (global as any).gitwhisperExtensionContext;
  }

  /**
   * Initialize the config manager with extension context
   */
  static initialize(context: vscode.ExtensionContext): void {
    (global as any).gitwhisperExtensionContext = context;
  }

  /**
   * Get keyboard shortcuts configuration
   */
  getKeyboardShortcuts(): { [command: string]: string } {
    const config = this.getConfig();
    return (
      config.get<{ [command: string]: string }>("keyboardShortcuts") || {
        generateCommit: "G+C",
        analyzeChanges: "G+A",
        quickCommitFix: "G+F",
        quickCommitFeature: "G+N",
        quickCommitRefactor: "G+R",
        quickCommitDocs: "G+D",
        quickCommitTest: "G+T",
        quickCommitChore: "G+H",
      }
    );
  }

  /**
   * Set keyboard shortcuts configuration
   */
  async setKeyboardShortcuts(shortcuts: {
    [command: string]: string;
  }): Promise<void> {
    const config = this.getConfig();
    try {
      await config.update(
        "keyboardShortcuts",
        shortcuts,
        vscode.ConfigurationTarget.Global
      );
      vscode.window.showInformationMessage(
        "Keyboard shortcuts updated successfully"
      );
    } catch (error) {
      console.error("Error setting keyboard shortcuts:", error);
      vscode.window.showErrorMessage("Failed to save keyboard shortcuts.");
    }
  }

  /**
   * Get ignored files patterns
   */
  getIgnoredFiles(): string[] {
    const config = this.getConfig();
    return (
      config.get<string[]>("ignoredFiles") || [
        "package-lock.json",
        "yarn.lock",
        "pnpm-lock.yaml",
        "*.log",
        "*.tmp",
        "*.temp",
        ".DS_Store",
        "Thumbs.db",
      ]
    );
  }

  /**
   * Set ignored files patterns
   */
  async setIgnoredFiles(patterns: string[]): Promise<void> {
    const config = this.getConfig();
    try {
      await config.update(
        "ignoredFiles",
        patterns,
        vscode.ConfigurationTarget.Global
      );
      vscode.window.showInformationMessage(
        "Ignored files patterns updated successfully"
      );
    } catch (error) {
      console.error("Error setting ignored files:", error);
      vscode.window.showErrorMessage("Failed to save ignored files patterns.");
    }
  }

  /**
   * Add a file pattern to ignored files
   */
  async addIgnoredFile(pattern: string): Promise<void> {
    const currentPatterns = this.getIgnoredFiles();
    if (!currentPatterns.includes(pattern)) {
      currentPatterns.push(pattern);
      await this.setIgnoredFiles(currentPatterns);
    }
  }

  /**
   * Remove a file pattern from ignored files
   */
  async removeIgnoredFile(pattern: string): Promise<void> {
    const currentPatterns = this.getIgnoredFiles();
    const updatedPatterns = currentPatterns.filter((p) => p !== pattern);
    if (updatedPatterns.length !== currentPatterns.length) {
      await this.setIgnoredFiles(updatedPatterns);
    }
  }

  /**
   * Get all configuration as a summary object
   */
  async getConfigSummary(): Promise<{
    defaultModel?: string;
    defaultVariant?: string;
    language: Language;
    alwaysAdd: boolean;
    autoPush: boolean;
    ollamaBaseUrl: string;
    customOllamaVariant?: string;
    keyboardShortcuts: { [command: string]: string };
    ignoredFiles: string[];
    hasApiKeys: { [model: string]: boolean };
  }> {
    const config = this.getConfig();
    const models = [
      "openai",
      "claude",
      "gemini",
      "grok",
      "llama",
      "deepseek",
      "github",
      "ollama",
    ];
    const hasApiKeys: { [model: string]: boolean } = {};

    for (const model of models) {
      const requirements = CommitGeneratorFactory.getModelRequirements(model);
      if (requirements.requiresApiKey) {
        const apiKey = await this.getApiKey(model);
        const envKey = this.getEnvironmentApiKey(model);
        hasApiKeys[model] = !!(apiKey || envKey);
      } else {
        // Models that don't require API keys are always "available"
        hasApiKeys[model] = true;
      }
    }

    return {
      defaultModel: config.get<string>("defaultModel"),
      defaultVariant: config.get<string>("defaultVariant"),
      language: this.getWhisperLanguage(),
      alwaysAdd: this.shouldAlwaysAdd(),
      autoPush: this.shouldAutoPush(),
      ollamaBaseUrl: this.getOllamaBaseURL(),
      customOllamaVariant: this.getCustomOllamaVariant(),
      keyboardShortcuts: this.getKeyboardShortcuts(),
      ignoredFiles: this.getIgnoredFiles(),
      hasApiKeys,
    };
  }
}
