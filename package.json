{"name": "gitwhisper", "displayName": "GitWhisper", "description": "AI-powered Git commit message generator that whispers the perfect commit message based on your staged changes.", "version": "0.1.1", "publisher": "gitwhisper", "repository": {"type": "git", "url": "https://github.com/emacliam/gitwhisper-vscode.git"}, "engines": {"vscode": "^1.102.2"}, "categories": ["Other", "SCM Providers"], "keywords": ["git", "commit", "ai", "openai", "claude", "gemini", "conventional commits"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "gitwhisper.generateCommit", "title": "Generate AI Commit Message", "category": "GitWhisper", "icon": "$(sparkle)"}, {"command": "gitwhisper.analyzeChanges", "title": "Analyze Staged Changes", "category": "GitWhisper", "icon": "$(search)"}, {"command": "gitwhisper.configure", "title": "Configure GitWhisper", "category": "GitWhisper", "icon": "$(gear)"}, {"command": "gitwhisper.setApiKey", "title": "Set API Key", "category": "GitWhisper"}, {"command": "gitwhisper.selectModel", "title": "Select AI Model", "category": "GitWhisper"}, {"command": "gitwhisper.setLanguage", "title": "Set Commit Language", "category": "GitWhisper"}, {"command": "gitwhisper.openSettings", "title": "Open Settings Panel", "category": "GitWhisper"}, {"command": "gitwhisper.setGitHubPAT", "title": "Set GitHub Personal Access Token", "category": "GitWhisper"}, {"command": "gitwhisper.setCustomOllamaVariant", "title": "Set Custom Ollama Model", "category": "GitWhisper"}], "menus": {"scm/title": [{"command": "gitwhisper.generateCommit", "group": "navigation", "when": "scmProvider == git"}, {"command": "gitwhisper.analyzeChanges", "group": "navigation", "when": "scmProvider == git"}], "commandPalette": [{"command": "gitwhisper.generateCommit", "when": "gitOpenRepositoryCount != 0"}, {"command": "gitwhisper.analyzeChanges", "when": "gitOpenRepositoryCount != 0"}]}, "configuration": {"title": "GitWhisper", "properties": {"gitwhisper.defaultModel": {"type": "string", "default": "openai", "enum": ["openai", "claude", "gemini", "grok", "llama", "deepseek", "github", "ollama"], "description": "Default AI model to use for commit message generation"}, "gitwhisper.defaultVariant": {"type": "string", "default": "gpt-4o", "description": "Default model variant to use"}, "gitwhisper.language": {"type": "string", "default": "en;US", "description": "Language for commit messages (format: code;countryCode)"}, "gitwhisper.alwaysAdd": {"type": "boolean", "default": false, "description": "Automatically stage unstaged changes before generating commit"}, "gitwhisper.autoPush": {"type": "boolean", "default": false, "description": "Automatically push commits after creation"}, "gitwhisper.ollamaBaseUrl": {"type": "string", "default": "http://localhost:11434", "description": "Base URL for Ollama API"}, "gitwhisper.customOllamaVariant": {"type": "string", "default": "", "description": "Custom Ollama model variant (leave empty to use predefined variants)"}}}}, "activationEvents": ["onCommand:gitwhisper.generateCommit", "onCommand:gitwhisper.analyzeChanges", "onCommand:gitwhisper.configure", "onCommand:gitwhisper.setApiKey", "onCommand:gitwhisper.selectModel", "onCommand:gitwhisper.setLanguage", "onCommand:gitwhisper.openSettings", "onCommand:gitwhisper.setGitHubPAT", "onCommand:gitwhisper.setCustomOllamaVariant"], "scripts": {"vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.102.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "typescript": "^5.8.3"}, "dependencies": {"axios": "^1.6.0", "simple-git": "^3.28.0"}, "packageManager": "pnpm@10.6.2+sha512.47870716bea1572b53df34ad8647b42962bc790ce2bf4562ba0f643237d7302a3d6a8ecef9e4bdfc01d23af1969aa90485d4cebb0b9638fa5ef1daef656f6c1b"}