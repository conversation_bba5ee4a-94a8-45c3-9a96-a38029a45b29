# Changelog

All notable changes to the GitWhisper VS Code extension will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.1.0] - 2025-07-28

### Added

- **Complete AI Provider Implementation**: All 8 AI providers are now fully implemented and functional
  - ✅ OpenAI (GPT-3.5, GPT-4, GPT-4o variants)
  - ✅ <PERSON> (Claude 3 Haiku, Sonnet, Opus variants)
  - ✅ Google Gemini (Gemini 1.5 Flash, Pro variants)
  - ✅ xAI Grok (Grok Beta, Grok Vision variants)
  - ✅ Meta Llama (Llama 3.1, 3.2 variants)
  - ✅ DeepSeek (DeepSeek Chat, Coder variants)
  - ✅ GitHub Models (GPT-4o, Claude, Llama variants)
  - ✅ Ollama (Local AI models support)

### Changed

- Updated CommitGeneratorFactory to instantiate all AI provider generators
- Enhanced error handling and validation for all model types
- Improved API key requirements validation per provider

## [0.0.1] - 2025-07-28

### Added

- Initial release of GitWhisper VS Code extension
- AI-powered commit message generation
- Support for multiple AI providers (OpenAI, <PERSON>, Gemini, Grok, Llama, DeepSeek, GitHub, Ollama)
- Multi-language support for commit messages (30+ languages)
- Smart Git integration with automatic staging
- Multi-repository support
- Secure API key storage using VS Code secrets
- Change analysis with AI
- Status bar integration
- Comprehensive error handling and validation
- Configuration management through VS Code settings
- Command palette integration
- Keyboard shortcuts for common actions

### Features

- **Generate Commit Message**: AI-powered conventional commit message generation
- **Analyze Changes**: Detailed analysis of staged changes
- **Set API Key**: Secure storage of API keys for different providers
- **Select Model**: Choose from multiple AI models and variants
- **Set Language**: Configure commit message language
- **Open Settings**: Access extension configuration panel

### Supported AI Providers

- **OpenAI**: GPT-3.5 Turbo, GPT-4, GPT-4 Turbo
- **Anthropic Claude**: Claude 3 Haiku, Sonnet, Opus
- **Google Gemini**: Gemini Pro, Gemini Pro Vision
- **xAI Grok**: Grok Beta
- **Meta Llama**: Llama 2, Code Llama
- **DeepSeek**: DeepSeek Coder, DeepSeek Chat
- **GitHub**: GitHub Models
- **Ollama**: Local model support

### Configuration Options

- Default AI model and variant selection
- Language preference for commit messages
- Auto-staging behavior
- Auto-push after commit
- Ollama base URL configuration
- Secure API key management

### Technical Features

- TypeScript implementation with full type safety
- Comprehensive error handling with user-friendly messages
- Input validation for API keys and configuration
- Retry mechanisms for API failures
- Model switching suggestions on errors
- Git repository detection and multi-repo support
- VS Code extension best practices compliance
